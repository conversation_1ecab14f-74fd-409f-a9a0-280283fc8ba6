/**
 * Production configuration overrides for the house rental crawler
 * This file extends the base configuration with production-specific settings
 * and environment variable support
 */

const baseConfig = require('./config');
require('dotenv').config();

// Helper function to parse boolean environment variables
const parseBoolean = (value, defaultValue = false) => {
  if (typeof value === 'string') {
    return value.toLowerCase() === 'true';
  }
  return defaultValue;
};

// Helper function to parse integer environment variables
const parseInteger = (value, defaultValue) => {
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
};

// Production configuration overrides
const productionConfig = {
  // Browser settings - optimized for production
  browser: {
    ...baseConfig.browser,
    headless: parseBoolean(process.env.HEADLESS, true),
    args: [
      ...baseConfig.browser.args,
      // Additional production-specific Chrome flags
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding',
      '--disable-features=TranslateUI',
      '--disable-ipc-flooding-protection',
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor',
      '--single-process', // Use with caution, may cause instability
      '--no-first-run',
      '--no-default-browser-check',
      '--disable-default-apps',
    ],
  },

  // Crawling settings - production optimized
  crawling: {
    ...baseConfig.crawling,
    delay: {
      min: parseInteger(process.env.DELAY_MIN, 3000), // Slightly longer delays in production
      max: parseInteger(process.env.DELAY_MAX, 7000),
    },
    retries: parseInteger(process.env.CRAWLER_RETRIES, 3),
    timeout: parseInteger(process.env.BROWSER_TIMEOUT, 45000), // Longer timeout for production
    maxPages: parseInteger(process.env.MAX_PAGES, 5),
  },

  // Output settings
  output: {
    ...baseConfig.output,
    directory: process.env.OUTPUT_DIR || './output',
    format: process.env.OUTPUT_FORMAT || 'json',
    filename: process.env.OUTPUT_FILENAME || 'rental_properties',
  },

  // Site-specific settings with environment variable support
  sites: {
    chotot: {
      ...baseConfig.sites.chotot,
      baseUrl: process.env.CRAWLER_BASE_URL || baseConfig.sites.chotot.baseUrl,
      maxScrolls: parseInteger(process.env.MAX_SCROLLS, 5),
    },
  },

  // Proxy configuration from environment
  proxy: {
    enabled: parseBoolean(process.env.PROXY_ENABLED, false),
    host: process.env.PROXY_HOST,
    port: parseInteger(process.env.PROXY_PORT, 8080),
    username: process.env.PROXY_USERNAME,
    password: process.env.PROXY_PASSWORD,
    type: process.env.PROXY_TYPE || 'http',
  },

  // CAPTCHA configuration from environment
  captcha: {
    enabled: parseBoolean(process.env.CAPTCHA_ENABLED, false),
    twoCaptcha: {
      apiKey: process.env.TWOCAPTCHA_API_KEY,
    },
    antiCaptcha: {
      apiKey: process.env.ANTICAPTCHA_API_KEY,
    },
    manual: {
      enabled: parseBoolean(process.env.CAPTCHA_MANUAL_ENABLED, true),
      timeout: parseInteger(process.env.CAPTCHA_MANUAL_TIMEOUT, 300000),
    },
  },

  // Production-specific settings
  production: {
    // Logging configuration
    logging: {
      level: process.env.LOG_LEVEL || 'info',
      toFile: parseBoolean(process.env.LOG_TO_FILE, true),
      filePath: process.env.LOG_FILE_PATH || './logs/crawler.log',
    },

    // Database settings
    database: {
      cleanupEnabled: parseBoolean(process.env.DB_CLEANUP_ENABLED, true),
      cleanupDays: parseInteger(process.env.DB_CLEANUP_DAYS, 30),
    },

    // Monitoring settings
    monitoring: {
      enabled: parseBoolean(process.env.ENABLE_MONITORING, false),
      healthCheckPort: parseInteger(process.env.HEALTH_CHECK_PORT, 3000),
    },

    // Notification settings
    notifications: {
      slack: {
        webhookUrl: process.env.SLACK_WEBHOOK_URL,
        batchSize: parseInteger(process.env.SLACK_BATCH_SIZE, 10),
        batchDelay: parseInteger(process.env.SLACK_BATCH_DELAY, 1000),
      },
      onError: parseBoolean(process.env.NOTIFY_ON_ERROR, true),
      onSuccess: parseBoolean(process.env.NOTIFY_ON_SUCCESS, true),
      onNoNew: parseBoolean(process.env.NOTIFY_ON_NO_NEW, false),
    },

    // Security settings
    security: {
      disableDevTools: parseBoolean(process.env.DISABLE_DEV_TOOLS, true),
    },

    // Scheduled crawler settings
    scheduler: {
      cronPattern: process.env.CRAWLER_SCHEDULE || '0 */4 * * *',
      maxPages: parseInteger(process.env.CRAWLER_MAX_PAGES, 5),
      timezone: process.env.TZ || 'Asia/Ho_Chi_Minh',
    },
  },
};

// Merge base config with production overrides
const config = {
  ...baseConfig,
  ...productionConfig,
  browser: { ...baseConfig.browser, ...productionConfig.browser },
  crawling: { ...baseConfig.crawling, ...productionConfig.crawling },
  output: { ...baseConfig.output, ...productionConfig.output },
  sites: {
    ...baseConfig.sites,
    chotot: { ...baseConfig.sites.chotot, ...productionConfig.sites.chotot },
  },
  proxy: { ...baseConfig.proxy, ...productionConfig.proxy },
  captcha: { ...baseConfig.captcha, ...productionConfig.captcha },
};

// Validation function
const validateConfig = () => {
  const errors = [];

  // Validate required Slack webhook URL
  if (!config.production.notifications.slack.webhookUrl) {
    errors.push('SLACK_WEBHOOK_URL is required for notifications');
  }

  // Validate proxy configuration if enabled
  if (config.proxy.enabled) {
    if (!config.proxy.host) {
      errors.push('PROXY_HOST is required when proxy is enabled');
    }
  }

  // Validate CAPTCHA configuration if enabled
  if (config.captcha.enabled) {
    if (!config.captcha.twoCaptcha.apiKey && !config.captcha.antiCaptcha.apiKey) {
      errors.push('CAPTCHA API key is required when CAPTCHA solving is enabled');
    }
  }

  if (errors.length > 0) {
    console.error('❌ Configuration validation failed:');
    errors.forEach(error => console.error(`   • ${error}`));
    return false;
  }

  return true;
};

// Validate configuration on load
if (process.env.NODE_ENV === 'production') {
  if (!validateConfig()) {
    console.error('❌ Exiting due to configuration errors');
    process.exit(1);
  }
}

module.exports = config;
