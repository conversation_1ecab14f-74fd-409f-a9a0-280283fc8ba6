/**
 * Configuration loader that selects the appropriate config based on environment
 */

require('dotenv').config();

const environment = process.env.NODE_ENV || 'development';

let config;

switch (environment) {
  case 'production':
    config = require('./config.production');
    console.log('🏭 Loaded production configuration');
    break;
  case 'development':
  default:
    config = require('./config');
    console.log('🔧 Loaded development configuration');
    break;
}

// Add environment info to config
config.environment = {
  name: environment,
  isProduction: environment === 'production',
  isDevelopment: environment === 'development',
  nodeVersion: process.version,
  platform: process.platform,
  arch: process.arch,
};

// Log configuration summary (without sensitive data)
if (config.environment.isProduction) {
  console.log('📊 Configuration Summary:');
  console.log(`   • Environment: ${config.environment.name}`);
  console.log(`   • Headless: ${config.browser.headless}`);
  console.log(`   • Max Pages: ${config.crawling.maxPages}`);
  console.log(`   • Proxy Enabled: ${config.proxy.enabled}`);
  console.log(`   • CAPTCHA Enabled: ${config.captcha.enabled}`);
  console.log(`   • Slack Notifications: ${!!config.production?.notifications?.slack?.webhookUrl}`);
  console.log(`   • Timezone: ${config.production?.scheduler?.timezone || 'UTC'}`);
}

module.exports = config;
