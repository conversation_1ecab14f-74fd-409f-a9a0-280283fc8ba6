/**
 * Slack notification service for rental property updates
 */

const axios = require("axios");
require("dotenv").config();

class SlackService {
  constructor() {
    this.webhookUrl = process.env.SLACK_WEBHOOK_URL;
    if (!this.webhookUrl) {
      throw new Error("SLACK_WEBHOOK_URL environment variable is required");
    }
    // Configuration for batch sending
    this.batchSize = 10; // Properties per message to avoid Slack limits
    this.delayBetweenBatches = 1000; // 1 second delay between batch messages
  }

  /**
   * Format property data for Slack message
   */
  formatProperty(property) {
    const cleanPrice = property.price
      ? property.price.replace(/[^\d,]/g, "")
      : "N/A";
    const cleanLocation = property.location
      ? property.location.split("•")[0].trim()
      : "N/A";

    // Create property block
    const propertyBlock = {
      type: "section",
      text: {
        type: "mrkdwn",
        text: `*${
          property.title
        }*\n💰 *${cleanPrice} triệu/tháng*\n📍 ${cleanLocation}\n🕒 ${new Date(
          property.extractedAt
        ).toLocaleString("vi-VN")}`,
      },
    };

    // Add image if available
    if (property.images && property.images.length > 0) {
      const validImage = property.images.find(
        (img) =>
          img &&
          img.startsWith("http") &&
          !img.includes("data:image/gif;base64")
      );

      if (validImage) {
        propertyBlock.accessory = {
          type: "image",
          image_url: validImage,
          alt_text: property.title,
        };
      }
    }

    // Add action button
    const actionBlock = {
      type: "actions",
      elements: [
        {
          type: "button",
          text: {
            type: "plain_text",
            text: "Xem chi tiết",
          },
          url: property.url,
          style: "primary",
        },
      ],
    };

    return [propertyBlock, actionBlock, { type: "divider" }];
  }

  /**
   * Send properties in batches to avoid Slack message size limits
   */
  async sendPropertiesInBatches(properties, messageType = "initial") {
    if (properties.length === 0) {
      return;
    }

    const totalProperties = properties.length;
    const propertiesWithImages = properties.filter(
      (p) =>
        p.images &&
        p.images.length > 0 &&
        p.images.some(
          (img) =>
            img.startsWith("http") && !img.includes("data:image/gif;base64")
        )
    ).length;

    // Split properties into batches
    const batches = [];
    for (let i = 0; i < properties.length; i += this.batchSize) {
      batches.push(properties.slice(i, i + this.batchSize));
    }

    console.log(
      `📤 Sending ${totalProperties} properties in ${batches.length} batch(es) to Slack...`
    );

    // Send each batch
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      const isFirstBatch = batchIndex === 0;
      const isLastBatch = batchIndex === batches.length - 1;

      try {
        await this.sendPropertyBatch(
          batch,
          batchIndex + 1,
          batches.length,
          totalProperties,
          propertiesWithImages,
          messageType,
          isFirstBatch,
          isLastBatch
        );

        // Add delay between batches to avoid rate limiting
        if (!isLastBatch) {
          await new Promise((resolve) =>
            setTimeout(resolve, this.delayBetweenBatches)
          );
        }
      } catch (error) {
        console.error(
          `❌ Failed to send batch ${batchIndex + 1}:`,
          error.message
        );
        // Continue with next batch instead of failing completely
      }
    }

    console.log(`✅ Completed sending ${totalProperties} properties to Slack`);
  }

  /**
   * Send a single batch of properties
   */
  async sendPropertyBatch(
    batch,
    batchNumber,
    totalBatches,
    totalProperties,
    propertiesWithImages,
    messageType,
    isFirstBatch,
    isLastBatch
  ) {
    let blocks = [];

    // Add header only for first batch
    if (isFirstBatch) {
      const headerTitle =
        messageType === "initial"
          ? "🏠 Rental Property Crawler - Initial Results"
          : "🆕 New Rental Properties Found!";

      const summaryText =
        messageType === "initial"
          ? `📊 *Summary:*\n• Total properties found: *${totalProperties}*\n• Properties with images: *${propertiesWithImages}*\n• Crawled at: ${new Date().toLocaleString(
              "vi-VN"
            )}`
          : `📊 *New Properties:*\n• Total new: *${totalProperties}*\n• With images: *${propertiesWithImages}*\n• Found at: ${new Date().toLocaleString(
              "vi-VN"
            )}`;

      blocks.push(
        {
          type: "header",
          text: {
            type: "plain_text",
            text: headerTitle,
          },
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: summaryText,
          },
        },
        {
          type: "divider",
        }
      );
    }

    // Add batch header for multi-batch messages
    if (totalBatches > 1) {
      blocks.push({
        type: "context",
        elements: [
          {
            type: "mrkdwn",
            text: `📦 *Batch ${batchNumber} of ${totalBatches}* (${batch.length} properties)`,
          },
        ],
      });
    }

    // Add properties
    batch.forEach((property) => {
      blocks.push(...this.formatProperty(property));
    });

    // Add footer for last batch
    if (isLastBatch && totalBatches > 1) {
      blocks.push({
        type: "context",
        elements: [
          {
            type: "mrkdwn",
            text: `✅ _All ${totalProperties} properties have been sent._`,
          },
        ],
      });
    }

    const message = {
      blocks: blocks,
    };

    await axios.post(this.webhookUrl, message);
    console.log(
      `✅ Sent batch ${batchNumber}/${totalBatches} (${batch.length} properties)`
    );
  }

  /**
   * Send initial crawl results to Slack
   */
  async sendInitialResults(properties) {
    try {
      await this.sendPropertiesInBatches(properties, "initial");
      console.log(
        `✅ Sent initial results to Slack: ${properties.length} properties`
      );
    } catch (error) {
      console.error(
        "❌ Failed to send initial results to Slack:",
        error.message
      );
      throw error;
    }
  }

  /**
   * Send new properties to Slack
   */
  async sendNewProperties(newProperties) {
    try {
      if (newProperties.length === 0) {
        // Send "no new properties" message
        const message = {
          blocks: [
            {
              type: "header",
              text: {
                type: "plain_text",
                text: "🔄 Rental Property Update",
              },
            },
            {
              type: "section",
              text: {
                type: "mrkdwn",
                text: `✅ *No new properties found*\nLast checked: ${new Date().toLocaleString(
                  "vi-VN"
                )}`,
              },
            },
          ],
        };

        await axios.post(this.webhookUrl, message);
        console.log('✅ Sent "no new properties" update to Slack');
        return;
      }

      // Send all new properties in batches
      await this.sendPropertiesInBatches(newProperties, "new");
      console.log(`✅ Sent ${newProperties.length} new properties to Slack`);
    } catch (error) {
      console.error(
        "❌ Failed to send new properties to Slack:",
        error.message
      );
      throw error;
    }
  }

  /**
   * Send error notification to Slack
   */
  async sendError(error, context = "") {
    try {
      const message = {
        blocks: [
          {
            type: "header",
            text: {
              type: "plain_text",
              text: "❌ Crawler Error",
            },
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `*Error occurred:* ${context}\n\`\`\`${
                error.message
              }\`\`\`\n*Time:* ${new Date().toLocaleString("vi-VN")}`,
            },
          },
        ],
      };

      await axios.post(this.webhookUrl, message);
      console.log("✅ Sent error notification to Slack");
    } catch (slackError) {
      console.error("❌ Failed to send error to Slack:", slackError.message);
    }
  }
}

module.exports = SlackService;
