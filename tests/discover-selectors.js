/**
 * Comprehensive selector discovery script
 */

const StealthBrowser = require('./src/utils/stealth');
const config = require('./src/config');

async function discoverSelectors() {
  const browser = new StealthBrowser(config);
  
  try {
    console.log('🚀 Launching browser...');
    const page = await browser.launch();
    
    console.log('🌐 Navigating to Chotot...');
    await browser.navigateWithRetry('https://www.nhatot.com/thue-bat-dong-san-quan-7-tp-ho-chi-minh?price=5000000-8000000');
    
    console.log('⏳ Waiting for page to fully load...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    // Get page info
    const pageInfo = await page.evaluate(() => ({
      title: document.title,
      url: window.location.href,
      bodyClasses: document.body.className,
      hasContent: document.body.textContent.length > 100
    }));
    
    console.log('📄 Page Info:', pageInfo);
    
    if (pageInfo.title.includes('Just a moment') || !pageInfo.hasContent) {
      console.log('❌ Page still showing Cloudflare challenge or empty content');
      return;
    }
    
    // Discover all possible property-related elements
    const elementAnalysis = await page.evaluate(() => {
      const results = {
        allElements: [],
        possiblePropertyCards: [],
        textContent: document.body.textContent.substring(0, 500)
      };
      
      // Get all elements with meaningful classes
      const allElements = document.querySelectorAll('*[class]');
      const elementMap = new Map();
      
      Array.from(allElements).forEach(el => {
        const className = el.className;
        if (className && typeof className === 'string') {
          const key = `${el.tagName.toLowerCase()}.${className}`;
          if (!elementMap.has(key)) {
            elementMap.set(key, {
              tagName: el.tagName,
              className: className,
              count: 1,
              sampleText: el.textContent.substring(0, 100).trim()
            });
          } else {
            elementMap.get(key).count++;
          }
        }
      });
      
      // Convert to array and sort by count
      results.allElements = Array.from(elementMap.values())
        .filter(item => item.count > 1 && item.sampleText.length > 10)
        .sort((a, b) => b.count - a.count)
        .slice(0, 20);
      
      // Look for elements that might be property cards
      const propertyKeywords = ['item', 'card', 'listing', 'property', 'ad', 'house', 'rent'];
      const possibleCards = [];
      
      propertyKeywords.forEach(keyword => {
        const elements = document.querySelectorAll(`[class*="${keyword}" i]`);
        if (elements.length > 0 && elements.length < 50) {
          Array.from(elements).slice(0, 5).forEach(el => {
            possibleCards.push({
              selector: `[class*="${keyword}"]`,
              tagName: el.tagName,
              className: el.className,
              textContent: el.textContent.substring(0, 150).trim(),
              hasLinks: el.querySelectorAll('a').length > 0,
              hasImages: el.querySelectorAll('img').length > 0
            });
          });
        }
      });
      
      results.possiblePropertyCards = possibleCards;
      
      return results;
    });
    
    console.log('\n📊 Element Analysis:');
    console.log('Page text preview:', elementAnalysis.textContent);
    
    console.log('\n🔍 Most common elements:');
    elementAnalysis.allElements.forEach(item => {
      console.log(`${item.tagName}.${item.className} (${item.count}x): ${item.sampleText}`);
    });
    
    console.log('\n🏠 Possible property cards:');
    elementAnalysis.possiblePropertyCards.forEach(item => {
      console.log(`${item.selector} - ${item.tagName}.${item.className}`);
      console.log(`  Text: ${item.textContent}`);
      console.log(`  Has links: ${item.hasLinks}, Has images: ${item.hasImages}`);
      console.log('---');
    });
    
    // Try to find price-like elements
    const priceElements = await page.evaluate(() => {
      const pricePatterns = [/\d+[.,]\d+[.,]\d+/, /\d+\s*triệu/, /\d+\s*tr/, /\$\d+/, /₫\d+/];
      const elements = [];
      
      document.querySelectorAll('*').forEach(el => {
        const text = el.textContent.trim();
        if (text.length < 100 && pricePatterns.some(pattern => pattern.test(text))) {
          elements.push({
            tagName: el.tagName,
            className: el.className,
            textContent: text,
            selector: el.className ? `.${el.className.split(' ')[0]}` : el.tagName.toLowerCase()
          });
        }
      });
      
      return elements.slice(0, 10);
    });
    
    console.log('\n💰 Price-like elements:');
    priceElements.forEach(item => {
      console.log(`${item.selector}: ${item.textContent}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await browser.close();
  }
}

discoverSelectors();
