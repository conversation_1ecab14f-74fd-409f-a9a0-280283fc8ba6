[Unit]
Description=Chotot House Rent Crawler
Documentation=https://github.com/your-username/chotot-house-rent
After=network.target
Wants=network.target

[Service]
# Service configuration
Type=simple
User=crawler
Group=crawler
WorkingDirectory=/usr/src/app

# Environment
Environment=NODE_ENV=production
Environment=PATH=/usr/bin:/usr/local/bin
EnvironmentFile=-/usr/src/app/.env

# Execution
ExecStart=/usr/bin/node app.js
ExecReload=/bin/kill -HUP $MAINPID

# Restart policy
Restart=always
RestartSec=10
StartLimitInterval=60s
StartLimitBurst=3

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096
MemoryLimit=2G
CPUQuota=100%

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/usr/src/app/data /usr/src/app/output /usr/src/app/logs /usr/src/app/sessions

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=chotot-crawler

# Process management
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

[Install]
WantedBy=multi-user.target
