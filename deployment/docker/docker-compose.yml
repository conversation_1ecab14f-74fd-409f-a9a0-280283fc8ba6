version: '3.8'

services:
  chotot-crawler:
    build:
      context: ../..
      dockerfile: deployment/docker/Dockerfile
    container_name: chotot-house-rent-crawler
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - TZ=Asia/Ho_Chi_Minh
    env_file:
      - .env
    volumes:
      # Persist data directories
      - ./data:/usr/src/app/data
      - ./output:/usr/src/app/output
      - ./logs:/usr/src/app/logs
      - ./sessions:/usr/src/app/sessions
      # Configuration files (optional overrides)
      - ./urls.json:/usr/src/app/urls.json:ro
      - ./proxy.config.js:/usr/src/app/proxy.config.js:ro
      - ./captcha.config.js:/usr/src/app/captcha.config.js:ro
    # Uncomment if you need to expose a web interface
    # ports:
    #   - "3000:3000"
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    # Security options
    security_opt:
      - no-new-privileges:true
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    # Health check
    healthcheck:
      test: ["CMD", "node", "-e", "console.log('Health check passed')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a monitoring service
  # watchtower:
  #   image: containrrr/watchtower
  #   container_name: watchtower
  #   restart: unless-stopped
  #   volumes:
  #     - /var/run/docker.sock:/var/run/docker.sock
  #   environment:
  #     - WATCHTOWER_CLEANUP=true
  #     - WATCHTOWER_POLL_INTERVAL=3600
  #   command: chotot-house-rent-crawler

networks:
  default:
    name: chotot-crawler-network
