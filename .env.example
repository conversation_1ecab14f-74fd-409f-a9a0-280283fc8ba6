# Environment Configuration for Chotot House Rent Crawler

# =============================================================================
# SLACK CONFIGURATION (Required for notifications)
# =============================================================================
# Get your webhook URL from: https://api.slack.com/apps -> Your App -> Incoming Webhooks
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Environment (development, production)
NODE_ENV=production

# Timezone for scheduling and logging
TZ=Asia/Ho_Chi_Minh

# =============================================================================
# CRAWLER CONFIGURATION
# =============================================================================
# Base URL for crawling (can be overridden in scheduled-crawler.js)
CRAWLER_BASE_URL=https://www.nhatot.com/thue-bat-dong-san-quan-7-tp-ho-chi-minh?price=5000000-8000000

# Maximum pages to crawl per cycle
CRAWLER_MAX_PAGES=5

# Crawl schedule (cron format) - default: every 4 hours
CRAWLER_SCHEDULE=0 */4 * * *

# Browser settings
HEADLESS=true
BROWSER_TIMEOUT=30000

# Crawling settings
MAX_PAGES=10
DELAY_MIN=2000
DELAY_MAX=5000

# =============================================================================
# PROXY CONFIGURATION (Optional)
# =============================================================================
# Enable proxy usage
PROXY_ENABLED=false

# Proxy settings
PROXY_HOST=your_proxy_host
PROXY_PORT=8080
PROXY_USERNAME=your_username
PROXY_PASSWORD=your_password
PROXY_TYPE=http

# =============================================================================
# CAPTCHA CONFIGURATION (Optional)
# =============================================================================
# Enable CAPTCHA solving
CAPTCHA_ENABLED=false

# 2Captcha API Key
TWOCAPTCHA_API_KEY=your_2captcha_api_key_here

# Anti-Captcha API Key
ANTICAPTCHA_API_KEY=your_anticaptcha_api_key_here

# Manual CAPTCHA solving settings
CAPTCHA_MANUAL_ENABLED=true
CAPTCHA_MANUAL_TIMEOUT=300000

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# Log level (error, warn, info, debug)
LOG_LEVEL=info

# Enable file logging
LOG_TO_FILE=true

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Data cleanup settings
DB_CLEANUP_ENABLED=true
DB_CLEANUP_DAYS=30

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================
# Slack notification settings
SLACK_BATCH_SIZE=10
SLACK_BATCH_DELAY=1000

# Send notifications for errors
NOTIFY_ON_ERROR=true

# Send notifications for successful crawls
NOTIFY_ON_SUCCESS=true

# Send notifications when no new properties found
NOTIFY_ON_NO_NEW=false
