"Chromium Shortcut" = "Chromium शॉर्टकट";
CFBundleGetInfoString = "Google Chrome for Testing 137.0.7151.119, कॉपीराइट 2025 Google LLC. सर्व हक्क राखीव.";
NSBluetoothAlwaysUsageDescription = "एकदा का Chromium ला ॲक्सेस मिळाला की, वेबसाइट तुम्हाला ॲक्सेससाठी विचारू शकतील.";
NSBluetoothPeripheralUsageDescription = "एकदा का Chromium ला ॲक्सेस मिळाला की, वेबसाइट तुम्हाला ॲक्सेससाठी विचारू शकतील.";
NSCameraUsageDescription = "एकदा का Chromium ला ॲक्सेस मिळाला की, वेबसाइट तुम्हाला ॲक्सेससाठी विचारू शकतील.";
NSHumanReadableCopyright = "कॉपीराइट 2025 Google LLC. सर्व हक्क राखीव.";
NSLocalNetworkUsageDescription = "यामुळे तुम्हाला उपलब्ध डिव्हाइसमधून निवडता येईल आणि आशय दाखवता येईल.";
NSLocationUsageDescription = "एकदा का Chromium ला ॲक्सेस मिळाला की, वेबसाइट तुम्हाला ॲक्सेससाठी विचारू शकतील.";
NSMicrophoneUsageDescription = "एकदा का Chromium ला ॲक्सेस मिळाला की, वेबसाइट तुम्हाला ॲक्सेससाठी विचारू शकतील.";
NSWebBrowserPublicKeyCredentialUsageDescription = "एकदा का Chromium ला ॲक्सेस मिळाला की, वेबसाइट तुम्हाला ॲक्सेससाठी विचारू शकतील.";
