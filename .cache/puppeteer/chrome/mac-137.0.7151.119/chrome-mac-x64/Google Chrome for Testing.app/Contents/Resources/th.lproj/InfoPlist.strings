"Chromium Shortcut" = "ทางลัดของ Chromium";
CFBundleGetInfoString = "Google Chrome for Testing 137.0.7151.119, ลิขสิทธิ์ 2025 Google LLC สงวนลิขสิทธิ์";
NSBluetoothAlwaysUsageDescription = "เมื่อ Chromium มีสิทธิ์การเข้าถึงแล้ว เว็บไซต์จะขอสิทธิ์การเข้าถึงจากคุณได้";
NSBluetoothPeripheralUsageDescription = "เมื่อ Chromium มีสิทธิ์การเข้าถึงแล้ว เว็บไซต์จะขอสิทธิ์การเข้าถึงจากคุณได้";
NSCameraUsageDescription = "เมื่อ Chromium มีสิทธิ์การเข้าถึงแล้ว เว็บไซต์จะขอสิทธิ์การเข้าถึงจากคุณได้";
NSHumanReadableCopyright = "ลิขสิทธิ์ 2025 Google LLC สงวนลิขสิทธิ์";
NSLocalNetworkUsageDescription = "สิทธิ์นี้จะช่วยให้คุณสามารถเลือกจากอุปกรณ์ที่พร้อมใช้งานและแสดงเนื้อหาบนอุปกรณ์เหล่านั้น";
NSLocationUsageDescription = "เมื่อ Chromium มีสิทธิ์การเข้าถึงแล้ว เว็บไซต์จะขอสิทธิ์การเข้าถึงจากคุณได้";
NSMicrophoneUsageDescription = "เมื่อ Chromium มีสิทธิ์การเข้าถึงแล้ว เว็บไซต์จะขอสิทธิ์การเข้าถึงจากคุณได้";
NSWebBrowserPublicKeyCredentialUsageDescription = "เมื่อ Chromium มีสิทธิ์การเข้าถึงแล้ว เว็บไซต์จะขอสิทธิ์การเข้าถึงจากคุณได้";
