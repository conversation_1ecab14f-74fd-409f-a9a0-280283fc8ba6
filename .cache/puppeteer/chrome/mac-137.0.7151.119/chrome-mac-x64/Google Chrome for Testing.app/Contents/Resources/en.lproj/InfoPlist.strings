"Chromium Shortcut" = "Chromium Shortcut";
CFBundleGetInfoString = "Google Chrome for Testing 137.0.7151.119, Copyright 2025 Google LLC. All rights reserved.";
NSBluetoothAlwaysUsageDescription = "Once Chromium has access, websites will be able to ask you for access.";
NSBluetoothPeripheralUsageDescription = "Once Chromium has access, websites will be able to ask you for access.";
NSCameraUsageDescription = "Once Chromium has access, websites will be able to ask you for access.";
NSHumanReadableCopyright = "Copyright 2025 Google LLC. All rights reserved.";
NSLocalNetworkUsageDescription = "This will allow you to select from available devices and display content on them.";
NSLocationUsageDescription = "Once Chromium has access, websites will be able to ask you for access.";
NSMicrophoneUsageDescription = "Once Chromium has access, websites will be able to ask you for access.";
NSWebBrowserPublicKeyCredentialUsageDescription = "Once Chromium has access, websites will be able to ask you for access.";
