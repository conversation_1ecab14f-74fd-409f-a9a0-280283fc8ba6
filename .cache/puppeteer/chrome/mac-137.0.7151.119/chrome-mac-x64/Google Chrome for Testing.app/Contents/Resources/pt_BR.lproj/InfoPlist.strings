"Chromium Shortcut" = "Atalho do Chromium";
CFBundleGetInfoString = "Google Chrome for Testing 137.0.7151.119, Copyright 2025 Google LLC. Todos os direitos reservados.";
NSBluetoothAlwaysUsageDescription = "Assim que o Chromium tiver acesso, os sites poderão solicitar acesso.";
NSBluetoothPeripheralUsageDescription = "Assim que o Chromium tiver acesso, os sites poderão solicitar acesso.";
NSCameraUsageDescription = "Assim que o Chromium tiver acesso, os sites poderão solicitar acesso.";
NSHumanReadableCopyright = "Copyright 2025 Google LLC. Todos os direitos reservados.";
NSLocalNetworkUsageDescription = "Assim, você pode selecionar os dispositivos disponíveis e mostrar conteúdo neles.";
NSLocationUsageDescription = "Assim que o Chromium tiver acesso, os sites poderão solicitar acesso.";
NSMicrophoneUsageDescription = "Assim que o Chromium tiver acesso, os sites poderão solicitar acesso.";
NSWebBrowserPublicKeyCredentialUsageDescription = "Assim que o Chromium tiver acesso, os sites poderão solicitar acesso.";
