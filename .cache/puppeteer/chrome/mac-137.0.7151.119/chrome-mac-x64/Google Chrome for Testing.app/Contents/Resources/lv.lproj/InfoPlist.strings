"Chromium Shortcut" = "Chromium saīsne";
CFBundleGetInfoString = "Google Chrome for Testing 137.0.7151.119, Autortiesības: 2025 Google LLC. Visas tiesības paturētas.";
NSBluetoothAlwaysUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piekļ<PERSON>, vietnes varēs lūgt jums piekļuvi.";
NSBluetoothPeripheralUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piekļuve, vietnes varēs lūgt jums piekļuvi.";
NSCameraUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piekļ<PERSON>, vietnes varēs lūgt jums piekļuvi.";
NSHumanReadableCopyright = "Autortiesības: 2025 Google LLC. Visas tiesības paturētas.";
NSLocalNetworkUsageDescription = "Tādējādi varēsiet atlasīt kādu no pieejamām ierīcēm un parādīt tajā saturu.";
NSLocationUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piekļ<PERSON>, vietnes varēs lūgt jums piekļuvi.";
NSMicrophoneUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piekļuve, vietnes varēs lūgt jums piekļuvi.";
NSWebBrowserPublicKeyCredentialUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piekļuve, vietnes varēs lūgt jums piekļuvi.";
