"Chromium Shortcut" = "Zkratka prohlížeče Chromium";
CFBundleGetInfoString = "Google Chrome for Testing 137.0.7151.119, Copyright 2025 Google LLC. Všechna práva vyhrazena.";
NSBluetoothAlwaysUsageDescription = "A<PERSON> bude mít Chromium přístup, budou vás weby moci požádat o přístup.";
NSBluetoothPeripheralUsageDescription = "Až bude mít Chromium přístup, budou vás weby moci požádat o přístup.";
NSCameraUsageDescription = "Až bude mít Chromium přístup, budou vás weby moci požádat o přístup.";
NSHumanReadableCopyright = "Copyright 2025 Google LLC. Všechna práva vyhrazena.";
NSLocalNetworkUsageDescription = "Budete moci vybírat z dostupných zařízení a zobrazovat na nich obsah.";
NSLocationUsageDescription = "Až bude mít Chromium přístup, budou vás weby moci požádat o přístup.";
NSMicrophoneUsageDescription = "Až bude mít Chromium přístup, budou vás weby moci požádat o přístup.";
NSWebBrowserPublicKeyCredentialUsageDescription = "Až bude mít Chromium přístup, budou vás weby moci požádat o přístup.";
