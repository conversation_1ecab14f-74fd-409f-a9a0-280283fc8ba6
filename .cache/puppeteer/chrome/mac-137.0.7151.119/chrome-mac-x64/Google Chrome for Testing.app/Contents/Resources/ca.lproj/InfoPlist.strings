"Chromium Shortcut" = "Drecera de Chromium";
CFBundleGetInfoString = "Google Chrome for Testing 137.0.7151.119, Copyright 2025 Google LLC. Tots els drets reservats.";
NSBluetoothAlwaysUsageDescription = "Un cop Chromium tingui accés, els llocs web et podran demanar accés.";
NSBluetoothPeripheralUsageDescription = "Un cop Chromium tingui accés, els llocs web et podran demanar accés.";
NSCameraUsageDescription = "Un cop Chromium tingui accés, els llocs web et podran demanar accés.";
NSHumanReadableCopyright = "Copyright 2025 Google LLC. Tots els drets reservats.";
NSLocalNetworkUsageDescription = "Això et permetrà seleccionar un dels dispositius disponibles i mostrar-hi contingut.";
NSLocationUsageDescription = "Un cop Chromium tingui accés, els llocs web et podran demanar accés.";
NSMicrophoneUsageDescription = "Un cop Chromium tingui accés, els llocs web et podran demanar accés.";
NSWebBrowserPublicKeyCredentialUsageDescription = "Un cop Chromium tingui accés, els llocs web et podran demanar accés.";
