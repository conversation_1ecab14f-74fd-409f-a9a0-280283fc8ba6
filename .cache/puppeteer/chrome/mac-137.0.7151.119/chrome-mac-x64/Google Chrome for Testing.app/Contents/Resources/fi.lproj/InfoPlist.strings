"Chromium Shortcut" = "Chromium-pikanäppäin";
CFBundleGetInfoString = "Google Chrome for Testing 137.0.7151.119, Copyright 2025 Google LLC. Kaikki oikeudet pidätetään.";
NSBluetoothAlwaysUsageDescription = "Kun Chromiumilla on käyttöoikeus, sivustot voivat pyytää sitä sinulta.";
NSBluetoothPeripheralUsageDescription = "Kun Chromiumilla on käyttöoikeus, sivustot voivat pyytää sitä sinulta.";
NSCameraUsageDescription = "Kun Chromiumilla on käyttöoikeus, sivustot voivat pyytää sitä sinulta.";
NSHumanReadableCopyright = "Copyright 2025 Google LLC. Kaikki oikeudet pidätetään.";
NSLocalNetworkUsageDescription = "Näin voit valita käytettävissä olevista laitteista ja näyttää niillä sisältöä.";
NSLocationUsageDescription = "Kun Chromiumilla on käyttöoikeus, sivustot voivat pyytää sitä sinulta.";
NSMicrophoneUsageDescription = "Kun Chromiumilla on käyttöoikeus, sivustot voivat pyytää sitä sinulta.";
NSWebBrowserPublicKeyCredentialUsageDescription = "Kun Chromiumilla on käyttöoikeus, sivustot voivat pyytää sitä sinulta.";
