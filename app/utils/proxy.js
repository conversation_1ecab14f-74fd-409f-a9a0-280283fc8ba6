/**
 * Proxy management utilities for bypassing Cloudflare
 */

class ProxyManager {
  constructor(config) {
    this.config = config.proxy || {};
    this.currentProxy = null;
    this.proxyList = [];
    this.failedProxies = new Set();
  }

  /**
   * Initialize proxy list from config or file
   */
  async initialize() {
    if (this.config.list && Array.isArray(this.config.list)) {
      this.proxyList = this.config.list;
    } else if (this.config.file) {
      await this.loadProxyFile(this.config.file);
    } else if (this.config.host && this.config.port) {
      this.proxyList = [{
        host: this.config.host,
        port: this.config.port,
        username: this.config.username,
        password: this.config.password,
        type: this.config.type || 'http'
      }];
    }

    console.log(`📡 Loaded ${this.proxyList.length} proxy(ies)`);
  }

  /**
   * Load proxies from file
   */
  async loadProxyFile(filePath) {
    try {
      const fs = require('fs-extra');
      const content = await fs.readFile(filePath, 'utf8');
      const lines = content.split('\n').filter(line => line.trim());
      
      this.proxyList = lines.map(line => {
        const parts = line.trim().split(':');
        if (parts.length >= 2) {
          return {
            host: parts[0],
            port: parts[1],
            username: parts[2] || null,
            password: parts[3] || null,
            type: 'http'
          };
        }
        return null;
      }).filter(Boolean);
    } catch (error) {
      console.error('❌ Failed to load proxy file:', error);
    }
  }

  /**
   * Get next available proxy
   */
  getNextProxy() {
    if (!this.config.enabled || this.proxyList.length === 0) {
      return null;
    }

    // Filter out failed proxies
    const availableProxies = this.proxyList.filter(proxy => 
      !this.failedProxies.has(`${proxy.host}:${proxy.port}`)
    );

    if (availableProxies.length === 0) {
      console.log('⚠️ All proxies failed, clearing failed list');
      this.failedProxies.clear();
      return this.proxyList[0];
    }

    // Round-robin selection
    const index = Math.floor(Math.random() * availableProxies.length);
    this.currentProxy = availableProxies[index];
    
    console.log(`🔄 Using proxy: ${this.currentProxy.host}:${this.currentProxy.port}`);
    return this.currentProxy;
  }

  /**
   * Mark current proxy as failed
   */
  markCurrentProxyFailed() {
    if (this.currentProxy) {
      const proxyKey = `${this.currentProxy.host}:${this.currentProxy.port}`;
      this.failedProxies.add(proxyKey);
      console.log(`❌ Marked proxy as failed: ${proxyKey}`);
    }
  }

  /**
   * Get proxy arguments for Puppeteer
   */
  getProxyArgs() {
    const proxy = this.getNextProxy();
    if (!proxy) return [];

    return [`--proxy-server=${proxy.type}://${proxy.host}:${proxy.port}`];
  }

  /**
   * Get proxy authentication
   */
  getProxyAuth() {
    if (!this.currentProxy || !this.currentProxy.username) {
      return null;
    }

    return {
      username: this.currentProxy.username,
      password: this.currentProxy.password
    };
  }

  /**
   * Test proxy connectivity
   */
  async testProxy(proxy) {
    try {
      const puppeteer = require('puppeteer');
      const browser = await puppeteer.launch({
        headless: true,
        args: [`--proxy-server=http://${proxy.host}:${proxy.port}`]
      });

      const page = await browser.newPage();
      
      if (proxy.username) {
        await page.authenticate({
          username: proxy.username,
          password: proxy.password
        });
      }

      await page.goto('https://httpbin.org/ip', { timeout: 10000 });
      const content = await page.content();
      
      await browser.close();
      
      return content.includes('origin');
    } catch (error) {
      return false;
    }
  }

  /**
   * Test all proxies and remove failed ones
   */
  async validateProxies() {
    console.log('🧪 Testing proxy connectivity...');
    const validProxies = [];

    for (const proxy of this.proxyList) {
      const isValid = await this.testProxy(proxy);
      if (isValid) {
        validProxies.push(proxy);
        console.log(`✅ Proxy valid: ${proxy.host}:${proxy.port}`);
      } else {
        console.log(`❌ Proxy failed: ${proxy.host}:${proxy.port}`);
      }
    }

    this.proxyList = validProxies;
    console.log(`📊 ${validProxies.length}/${this.proxyList.length} proxies are valid`);
  }
}

module.exports = ProxyManager;
