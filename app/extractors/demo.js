/**
 * Demo extractor for testing the crawler framework
 * This extracts quotes from quotes.toscrape.com as a demonstration
 */

class DemoExtractor {
  constructor(config) {
    this.config = {
      selectors: {
        propertyCard: '.quote',
        title: '.text',
        price: '.author',
        location: '.tags',
        area: '',
        description: '',
        images: '',
        nextPage: '.next a',
        loadMoreButton: ''
      },
      waitForSelector: '.quote',
      maxScrolls: 2
    };
  }

  async extractPropertyData(page) {
    try {
      console.log('🔍 Extracting demo data...');

      // Wait for quotes to load
      await page.waitForSelector(this.config.selectors.propertyCard, { timeout: 10000 });

      // Extract all quote data (simulating property data)
      const properties = await page.evaluate((selectors) => {
        const quoteCards = document.querySelectorAll(selectors.propertyCard);
        const results = [];

        quoteCards.forEach((card, index) => {
          try {
            const property = {
              id: `demo_${Date.now()}_${index}`,
              source: 'quotes.toscrape.com',
              extractedAt: new Date().toISOString()
            };

            // Extract quote text as "title"
            const titleElement = card.querySelector(selectors.title);
            property.title = titleElement ? titleElement.textContent.trim() : '';

            // Extract author as "price"
            const priceElement = card.querySelector(selectors.price);
            property.price = priceElement ? priceElement.textContent.trim() : '';

            // Extract tags as "location"
            const locationElement = card.querySelector(selectors.location);
            property.location = locationElement ? locationElement.textContent.trim() : '';

            // Set demo values for other fields
            property.area = `${Math.floor(Math.random() * 100) + 50}m²`;
            property.description = `Demo property description for ${property.title.substring(0, 30)}...`;
            property.images = [];
            property.url = window.location.href;

            // Only add if we have essential data
            if (property.title && property.price) {
              results.push(property);
            }
          } catch (error) {
            console.error('Error extracting demo property:', error);
          }
        });

        return results;
      }, this.config.selectors);

      console.log(`✅ Extracted ${properties.length} demo properties`);
      return properties;
    } catch (error) {
      console.error('❌ Error extracting demo data:', error);
      return [];
    }
  }

  async hasNextPage(page) {
    try {
      const nextButton = await page.$(this.config.selectors.nextPage);
      return !!nextButton;
    } catch (error) {
      return false;
    }
  }

  async goToNextPage(page) {
    try {
      const nextButton = await page.$(this.config.selectors.nextPage);
      if (nextButton) {
        await nextButton.click();
        await page.waitForNavigation({ waitUntil: 'networkidle2' });
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error navigating to next page:', error);
      return false;
    }
  }

  async scrollToLoadMore(page, maxScrolls = 2) {
    // Simple scroll for demo
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight);
    });
    await new Promise(resolve => setTimeout(resolve, 1000));
    return 1;
  }

  cleanPropertyData(property) {
    // Simple cleaning for demo
    return property;
  }
}

module.exports = DemoExtractor;
