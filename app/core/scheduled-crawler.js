/**
 * Scheduled rental property crawler with Slack notifications
 */

const cron = require("node-cron");
const path = require("path");
const RentalCrawler = require("./crawler");
const SlackService = require("../services/slack");
const DatabaseService = require("../services/database");
require("dotenv").config();

class ScheduledCrawler {
  constructor() {
    this.crawler = new RentalCrawler({
      enableProxy: false,
      enableCaptchaSolving: false,
    });
    this.slackService = new SlackService();
    this.dbService = new DatabaseService();
    this.isRunning = false;
    this.baseUrl =
      "https://www.nhatot.com/thue-bat-dong-san-quan-7-tp-ho-chi-minh?price=5000000-8000000";
  }

  /**
   * Initialize the scheduled crawler
   */
  async initialize() {
    try {
      console.log("🚀 Initializing Scheduled Rental Property Crawler...");

      // Initialize database
      await this.dbService.initializeDatabase();

      // Get stats
      const stats = await this.dbService.getStats();
      console.log("📊 Current stats:", stats);

      console.log("✅ Scheduled crawler initialized successfully");
      return true;
    } catch (error) {
      console.error(
        "❌ Failed to initialize scheduled crawler:",
        error.message
      );
      await this.slackService.sendError(error, "Initialization failed");
      return false;
    }
  }

  /**
   * Run a single crawl cycle
   */
  async runCrawlCycle(isInitial = false) {
    if (this.isRunning) {
      console.log("⏳ Crawl already in progress, skipping...");
      return;
    }

    this.isRunning = true;

    try {
      console.log(
        `\n🔄 Starting ${isInitial ? "initial" : "scheduled"} crawl cycle...`
      );

      const lastCrawlInfo = await this.dbService.getLastCrawlInfo();
      let currentPage = 1;
      let allNewProperties = [];
      let totalPropertiesFound = 0;

      // If this is not initial crawl and we have previous data, start from page 1
      // but continue to page 2+ if all items on page 1 are new
      if (!isInitial && lastCrawlInfo.lastCrawlTime) {
        console.log("📅 Previous crawl found, checking for new properties...");
      }

      // Crawl pages until we find some existing properties or reach max pages
      const maxPages = 5; // Limit to prevent infinite crawling
      let foundExistingProperties = false;

      while (currentPage <= maxPages && !foundExistingProperties) {
        console.log(`\n📄 Crawling page ${currentPage}...`);

        // Update crawler config for current page
        const pageUrl =
          currentPage === 1
            ? this.baseUrl
            : `${this.baseUrl}&page=${currentPage}`;
        this.crawler.config.sites.chotot.url = pageUrl;
        this.crawler.config.crawling.maxPages = 1; // Crawl one page at a time

        // Run crawler
        await this.crawler.run();

        // Get the latest crawled data
        const outputFiles = await this.getLatestOutputFile();
        if (!outputFiles) {
          console.log("❌ No output file found");
          break;
        }

        const fs = require("fs");
        const crawledData = JSON.parse(
          fs.readFileSync(
            outputFiles.replace("output/", path.join(__dirname, "../output/")),
            "utf8"
          )
        );
        const pageProperties = crawledData.properties || [];
        totalPropertiesFound += pageProperties.length;

        console.log(
          `📊 Found ${pageProperties.length} properties on page ${currentPage}`
        );

        if (pageProperties.length === 0) {
          console.log("📭 No properties found on this page, stopping...");
          break;
        }

        // Filter new properties
        const newProperties = await this.dbService.filterNewProperties(
          pageProperties
        );

        if (newProperties.length === 0) {
          console.log("✅ No new properties found on this page");
          foundExistingProperties = true;
        } else if (newProperties.length < pageProperties.length) {
          console.log(
            `🔍 Found ${newProperties.length} new out of ${pageProperties.length} total`
          );
          foundExistingProperties = true;
          allNewProperties.push(...newProperties);
        } else {
          console.log(
            `🆕 All ${newProperties.length} properties are new, checking next page...`
          );
          allNewProperties.push(...newProperties);
        }

        currentPage++;
      }

      console.log(`\n📊 Crawl cycle completed:`);
      console.log(`   • Pages crawled: ${currentPage - 1}`);
      console.log(`   • Total properties found: ${totalPropertiesFound}`);
      console.log(`   • New properties: ${allNewProperties.length}`);

      // Update database with new properties
      if (allNewProperties.length > 0) {
        await this.dbService.addSeenProperties(allNewProperties);
      }

      // Update last crawl info
      await this.dbService.updateLastCrawlInfo({
        lastPageCrawled: currentPage - 1,
        totalPropertiesFound: totalPropertiesFound,
        newPropertiesFound: allNewProperties.length,
      });

      // Send Slack notifications
      if (isInitial) {
        // For initial crawl, send all properties
        const allProperties =
          allNewProperties.length > 0 ? allNewProperties : [];
        await this.slackService.sendInitialResults(allProperties);
      } else {
        // For scheduled crawls, send only new properties
        await this.slackService.sendNewProperties(allNewProperties);
      }

      // Cleanup old properties
      await this.dbService.cleanupOldProperties();

      console.log("✅ Crawl cycle completed successfully\n");
    } catch (error) {
      console.error("❌ Crawl cycle failed:", error.message);
      await this.slackService.sendError(error, "Crawl cycle failed");
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Get the latest output file
   */
  async getLatestOutputFile() {
    try {
      const fs = require("fs");
      const path = require("path");
      const outputDir = path.join(__dirname, "../output");

      if (!fs.existsSync(outputDir)) {
        return null;
      }

      const files = fs
        .readdirSync(outputDir)
        .filter((file) => file.endsWith(".json"))
        .map((file) => ({
          name: file,
          path: path.join(outputDir, file),
          time: fs.statSync(path.join(outputDir, file)).mtime,
        }))
        .sort((a, b) => b.time - a.time);

      return files.length > 0 ? `output/${files[0].name}` : null;
    } catch (error) {
      console.error("❌ Failed to get latest output file:", error.message);
      return null;
    }
  }

  /**
   * Start the scheduled crawler
   */
  async start() {
    try {
      const initialized = await this.initialize();
      if (!initialized) {
        console.error("❌ Failed to initialize, exiting...");
        process.exit(1);
      }

      console.log("🎯 Starting scheduled crawler...");

      // Run initial crawl
      console.log("🚀 Running initial crawl...");
      await this.runCrawlCycle(true);

      // Schedule crawls every 4 hours
      console.log("⏰ Scheduling crawls every 4 hours...");
      cron.schedule("0 */4 * * *", async () => {
        console.log("\n⏰ Scheduled crawl triggered...");
        await this.runCrawlCycle(false);
      });

      console.log("✅ Scheduled crawler is now running!");
      console.log("📅 Next crawl will run in 4 hours");
      console.log("🛑 Press Ctrl+C to stop");

      // Keep the process alive
      process.on("SIGINT", () => {
        console.log("\n🛑 Stopping scheduled crawler...");
        process.exit(0);
      });
    } catch (error) {
      console.error("❌ Failed to start scheduled crawler:", error.message);
      await this.slackService.sendError(error, "Startup failed");
      process.exit(1);
    }
  }
}

module.exports = ScheduledCrawler;
