#!/usr/bin/env node

/**
 * Command Line Interface for the Rental Property Crawler
 */

const chalk = require("chalk");
const RentalCrawler = require("../core/crawler");
const SessionManager = require("../utils/session");

class CLI {
  constructor() {
    this.sessionManager = new SessionManager();
  }

  async run() {
    const args = process.argv.slice(2);
    const command = args[0];

    switch (command) {
      case "start":
      case "crawl":
        await this.startCrawling(args.slice(1));
        break;
      case "resume":
        await this.resumeSession(args[1]);
        break;
      case "sessions":
        await this.listSessions();
        break;
      case "session":
        await this.sessionInfo(args[1]);
        break;
      case "delete-session":
        await this.deleteSession(args[1]);
        break;
      case "test":
        await this.testConfiguration();
        break;
      case "help":
      default:
        this.showHelp();
        break;
    }
  }

  async startCrawling(args) {
    const options = this.parseOptions(args);

    console.log(chalk.blue.bold("\n🏠 Rental Property Crawler"));
    console.log(chalk.gray("Starting new crawling session...\n"));

    const crawler = new RentalCrawler(options);

    try {
      await crawler.run();
      process.exit(0);
    } catch (error) {
      console.error(chalk.red("\n💥 Crawling failed:"), error.message);
      process.exit(1);
    }
  }

  async resumeSession(sessionName) {
    if (!sessionName) {
      console.error(chalk.red("❌ Please specify a session name to resume"));
      console.log(chalk.gray("Usage: npm run resume <session-name>"));
      process.exit(1);
    }

    console.log(chalk.blue.bold("\n🏠 Rental Property Crawler"));
    console.log(chalk.gray(`Resuming session: ${sessionName}\n`));

    const crawler = new RentalCrawler({
      resumeSession: sessionName,
      createNewSession: false,
    });

    try {
      await crawler.run();
      process.exit(0);
    } catch (error) {
      console.error(chalk.red("\n💥 Session resume failed:"), error.message);
      process.exit(1);
    }
  }

  async listSessions() {
    console.log(chalk.blue.bold("\n📋 Available Sessions\n"));

    await this.sessionManager.initialize();
    const sessions = await this.sessionManager.listSessions();

    if (sessions.length === 0) {
      console.log(chalk.gray("No sessions found."));
      return;
    }

    for (const sessionName of sessions) {
      try {
        await this.sessionManager.loadSession(sessionName);
        const stats = this.sessionManager.getSessionStats();

        console.log(chalk.green(`📁 ${sessionName}`));
        console.log(
          chalk.gray(
            `   Started: ${new Date(stats.startTime).toLocaleString()}`
          )
        );
        console.log(
          chalk.gray(
            `   Last Activity: ${new Date(stats.lastActivity).toLocaleString()}`
          )
        );
        console.log(chalk.gray(`   URLs Crawled: ${stats.crawledUrls}`));
        console.log(chalk.gray(`   Properties: ${stats.totalProperties}`));
        console.log(chalk.gray(`   Failed URLs: ${stats.failedUrls}\n`));
      } catch (error) {
        console.log(chalk.red(`❌ ${sessionName} (corrupted)`));
      }
    }
  }

  async sessionInfo(sessionName) {
    if (!sessionName) {
      console.error(chalk.red("❌ Please specify a session name"));
      process.exit(1);
    }

    await this.sessionManager.initialize();
    const loaded = await this.sessionManager.loadSession(sessionName);

    if (!loaded) {
      console.error(chalk.red(`❌ Session '${sessionName}' not found`));
      process.exit(1);
    }

    const stats = this.sessionManager.getSessionStats();

    console.log(chalk.blue.bold(`\n📊 Session: ${sessionName}\n`));
    console.log(
      chalk.green(`Start Time: ${new Date(stats.startTime).toLocaleString()}`)
    );
    console.log(
      chalk.green(
        `Last Activity: ${new Date(stats.lastActivity).toLocaleString()}`
      )
    );
    console.log(chalk.green(`URLs Crawled: ${stats.crawledUrls}`));
    console.log(chalk.green(`Failed URLs: ${stats.failedUrls}`));
    console.log(chalk.green(`Total Properties: ${stats.totalProperties}`));
    console.log(chalk.green(`Sites: ${stats.sites}`));
  }

  async deleteSession(sessionName) {
    if (!sessionName) {
      console.error(chalk.red("❌ Please specify a session name to delete"));
      process.exit(1);
    }

    await this.sessionManager.initialize();
    const success = await this.sessionManager.deleteSession(sessionName);

    if (success) {
      console.log(
        chalk.green(`✅ Session '${sessionName}' deleted successfully`)
      );
    } else {
      console.error(chalk.red(`❌ Failed to delete session '${sessionName}'`));
      process.exit(1);
    }
  }

  async testConfiguration() {
    console.log(chalk.blue.bold("\n🧪 Testing Configuration\n"));

    const crawler = new RentalCrawler({
      createNewSession: false,
    });

    try {
      await crawler.initialize();
      console.log(chalk.green("✅ Browser launch: OK"));

      // Test navigation to a simple site
      await crawler.browser.navigateWithRetry("https://httpbin.org/user-agent");
      console.log(chalk.green("✅ Navigation: OK"));

      // Test proxy if configured
      if (crawler.proxyManager.config.enabled) {
        const response = await crawler.page.evaluate(
          () => document.body.textContent
        );
        console.log(chalk.green("✅ Proxy: OK"));
        console.log(
          chalk.gray(`User Agent: ${JSON.parse(response)["user-agent"]}`)
        );
      }

      await crawler.cleanup();
      console.log(chalk.green("\n✅ All tests passed!"));
    } catch (error) {
      console.error(chalk.red("\n❌ Test failed:"), error.message);
      process.exit(1);
    }
  }

  parseOptions(args) {
    const options = {};

    for (let i = 0; i < args.length; i++) {
      const arg = args[i];

      switch (arg) {
        case "--no-proxy":
          options.enableProxy = false;
          break;
        case "--enable-captcha":
          options.enableCaptchaSolving = true;
          break;
        case "--headless":
          options.headless = true;
          break;
        case "--no-headless":
          options.headless = false;
          break;
        case "--session":
          options.resumeSession = args[++i];
          break;
        case "--max-pages":
          options.maxPages = parseInt(args[++i]);
          break;
      }
    }

    return options;
  }

  showHelp() {
    console.log(chalk.blue.bold("\n🏠 Rental Property Crawler - Help\n"));

    console.log(chalk.green("Commands:"));
    console.log("  start, crawl          Start a new crawling session");
    console.log("  resume <session>      Resume an existing session");
    console.log("  sessions              List all available sessions");
    console.log("  session <name>        Show session information");
    console.log("  delete-session <name> Delete a session");
    console.log("  test                  Test configuration");
    console.log("  help                  Show this help message");

    console.log(chalk.green("\nOptions:"));
    console.log("  --no-proxy           Disable proxy usage");
    console.log("  --enable-captcha     Enable CAPTCHA solving");
    console.log("  --headless           Run in headless mode");
    console.log("  --no-headless        Run with visible browser");
    console.log("  --session <name>     Resume specific session");
    console.log("  --max-pages <num>    Limit pages per site");

    console.log(chalk.green("\nExamples:"));
    console.log("  npm start");
    console.log("  npm run crawl -- --no-proxy --max-pages 5");
    console.log("  npm run resume session_2024-01-15");
    console.log("  npm run sessions");
    console.log("  npm run test");

    console.log(chalk.gray("\nFor more information, see README.md"));
  }
}

// Run CLI if this file is executed directly
if (require.main === module) {
  const cli = new CLI();
  cli.run().catch((error) => {
    console.error(chalk.red("CLI Error:"), error);
    process.exit(1);
  });
}

module.exports = CLI;
