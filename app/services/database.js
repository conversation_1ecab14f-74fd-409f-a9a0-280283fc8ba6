/**
 * Simple file-based database service for tracking seen properties
 */

const fs = require("fs-extra");
const path = require("path");

class DatabaseService {
  constructor() {
    this.dbPath = path.join(process.cwd(), "data");
    this.seenPropertiesFile = path.join(this.dbPath, "seen_properties.json");
    this.lastCrawlFile = path.join(this.dbPath, "last_crawl.json");

    // Ensure data directory exists
    this.initializeDatabase();
  }

  /**
   * Initialize database directory and files
   */
  async initializeDatabase() {
    try {
      await fs.ensureDir(this.dbPath);

      // Initialize seen properties file if it doesn't exist
      if (!(await fs.pathExists(this.seenPropertiesFile))) {
        await fs.writeJson(this.seenPropertiesFile, {
          properties: {},
          lastUpdated: new Date().toISOString(),
        });
      }

      // Initialize last crawl file if it doesn't exist
      if (!(await fs.pathExists(this.lastCrawlFile))) {
        await fs.writeJson(this.lastCrawlFile, {
          lastCrawlTime: null,
          lastPageCrawled: 1,
          totalPropertiesFound: 0,
        });
      }

      console.log("📁 Database initialized successfully");
    } catch (error) {
      console.error("❌ Failed to initialize database:", error.message);
      throw error;
    }
  }

  /**
   * Get all seen properties
   */
  async getSeenProperties() {
    try {
      const data = await fs.readJson(this.seenPropertiesFile);
      return data.properties || {};
    } catch (error) {
      console.error("❌ Failed to read seen properties:", error.message);
      return {};
    }
  }

  /**
   * Save seen properties
   */
  async saveSeenProperties(properties) {
    try {
      const data = {
        properties: properties,
        lastUpdated: new Date().toISOString(),
      };
      await fs.writeJson(this.seenPropertiesFile, data, { spaces: 2 });
      console.log(`💾 Saved ${Object.keys(properties).length} seen properties`);
    } catch (error) {
      console.error("❌ Failed to save seen properties:", error.message);
      throw error;
    }
  }

  /**
   * Add new properties to seen list
   */
  async addSeenProperties(newProperties) {
    try {
      const seenProperties = await this.getSeenProperties();

      newProperties.forEach((property) => {
        // Use a combination of title and price as unique identifier
        const key = this.generatePropertyKey(property);
        seenProperties[key] = {
          id: property.id,
          title: property.title,
          price: property.price,
          location: property.location,
          url: property.url,
          firstSeen: new Date().toISOString(),
          source: property.source,
        };
      });

      await this.saveSeenProperties(seenProperties);
      return seenProperties;
    } catch (error) {
      console.error("❌ Failed to add seen properties:", error.message);
      throw error;
    }
  }

  /**
   * Filter out already seen properties
   */
  async filterNewProperties(properties) {
    try {
      const seenProperties = await this.getSeenProperties();
      const newProperties = [];

      properties.forEach((property) => {
        const key = this.generatePropertyKey(property);
        if (!seenProperties[key]) {
          newProperties.push(property);
        }
      });

      console.log(
        `🔍 Found ${newProperties.length} new properties out of ${properties.length} total`
      );
      return newProperties;
    } catch (error) {
      console.error("❌ Failed to filter new properties:", error.message);
      return properties; // Return all if filtering fails
    }
  }

  /**
   * Generate unique key for property
   */
  generatePropertyKey(property) {
    // Create a unique key based on title and price
    const title = (property.title || "").toLowerCase().trim();
    const price = (property.price || "").replace(/[^\d,]/g, "");
    const location = (property.location || "")
      .split("•")[0]
      .trim()
      .toLowerCase();

    return `${title}_${price}_${location}`.replace(/[^a-z0-9_,]/g, "");
  }

  /**
   * Get last crawl information
   */
  async getLastCrawlInfo() {
    try {
      return await fs.readJson(this.lastCrawlFile);
    } catch (error) {
      console.error("❌ Failed to read last crawl info:", error.message);
      return {
        lastCrawlTime: null,
        lastPageCrawled: 1,
        totalPropertiesFound: 0,
      };
    }
  }

  /**
   * Update last crawl information
   */
  async updateLastCrawlInfo(info) {
    try {
      const data = {
        lastCrawlTime: new Date().toISOString(),
        lastPageCrawled: info.lastPageCrawled || 1,
        totalPropertiesFound: info.totalPropertiesFound || 0,
        ...info,
      };

      await fs.writeJson(this.lastCrawlFile, data, { spaces: 2 });
      console.log(
        `📊 Updated last crawl info: page ${data.lastPageCrawled}, ${data.totalPropertiesFound} properties`
      );
    } catch (error) {
      console.error("❌ Failed to update last crawl info:", error.message);
      throw error;
    }
  }

  /**
   * Clean up old seen properties (older than 30 days)
   */
  async cleanupOldProperties() {
    try {
      const seenProperties = await this.getSeenProperties();
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      let cleanedCount = 0;
      const cleanedProperties = {};

      Object.entries(seenProperties).forEach(([key, property]) => {
        const firstSeen = new Date(property.firstSeen);
        if (firstSeen > thirtyDaysAgo) {
          cleanedProperties[key] = property;
        } else {
          cleanedCount++;
        }
      });

      if (cleanedCount > 0) {
        await this.saveSeenProperties(cleanedProperties);
        console.log(`🧹 Cleaned up ${cleanedCount} old properties`);
      }

      return cleanedCount;
    } catch (error) {
      console.error("❌ Failed to cleanup old properties:", error.message);
      return 0;
    }
  }

  /**
   * Get statistics
   */
  async getStats() {
    try {
      const seenProperties = await this.getSeenProperties();
      const lastCrawlInfo = await this.getLastCrawlInfo();

      return {
        totalSeenProperties: Object.keys(seenProperties).length,
        lastCrawlTime: lastCrawlInfo.lastCrawlTime,
        lastPageCrawled: lastCrawlInfo.lastPageCrawled,
        totalPropertiesFound: lastCrawlInfo.totalPropertiesFound,
      };
    } catch (error) {
      console.error("❌ Failed to get stats:", error.message);
      return {
        totalSeenProperties: 0,
        lastCrawlTime: null,
        lastPageCrawled: 1,
        totalPropertiesFound: 0,
      };
    }
  }
}

module.exports = DatabaseService;
