# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile
docker-compose*.yml
.dockerignore

# Documentation
README*.md
CHANGELOG.md
LICENSE
*.md

# Test files
test/
tests/
__tests__/
*.test.js
*.spec.js

# Build artifacts
dist/
build/

# Temporary files
tmp/
temp/

# Sessions and output (will be mounted as volumes)
sessions/
output/
data/

# Development files
.eslintrc*
.prettierrc*
.babelrc*
webpack.config.js
rollup.config.js
