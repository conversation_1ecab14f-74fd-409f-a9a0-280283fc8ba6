{"name": "chotot-house-rent", "version": "1.0.0", "main": "app/index.js", "description": "A web crawler for rental property data from Cloudflare-protected sites", "scripts": {"start": "node app/index.js", "start-scheduled": "NODE_ENV=production node app/index.js --scheduled", "start-cli": "node app/index.js start", "crawl": "node app/index.js crawl", "resume": "node app/index.js resume", "sessions": "node app/index.js sessions", "session": "node app/index.js session", "delete-session": "node app/index.js delete-session", "test-config": "node app/index.js test", "setup": "node scripts/setup.js", "analyze": "node scripts/analyze.js", "analyze-latest": "node scripts/analyze.js latest", "stats": "node scripts/analyze.js stats", "view": "node scripts/view-data.js", "view-latest": "node scripts/view-data.js latest", "serve": "node scripts/view-data.js serve", "dev": "node app/index.js", "test": "node test-system.js", "test-original": "echo \"Error: no test specified\" && exit 1", "docker:build": "docker build -f deployment/docker/Dockerfile -t chotot-house-rent-crawler .", "docker:run": "docker-compose -f deployment/docker/docker-compose.yml up -d", "docker:stop": "docker-compose -f deployment/docker/docker-compose.yml down", "docker:logs": "docker-compose -f deployment/docker/docker-compose.yml logs -f", "docker:restart": "docker-compose -f deployment/docker/docker-compose.yml restart", "production:start": "NODE_ENV=production node app/index.js --scheduled", "production:setup": "npm ci --only=production && npm run setup", "health-check": "node -e \"console.log('Health check passed')\"", "backup:data": "tar -czf backup-$(date +%Y%m%d-%H%M%S).tar.gz data/ output/ sessions/", "clean:logs": "find logs/ -name '*.log' -mtime +7 -delete 2>/dev/null || true", "clean:old-data": "find output/ -name '*.json' -mtime +30 -delete 2>/dev/null || true", "transfer": "./scripts/transfer.sh", "transfer:dry-run": "./scripts/transfer.sh --dry-run", "deploy:full": "./scripts/transfer.sh && echo 'Files transferred. Now run: ssh user@server \"cd /usr/src/app && sudo ./scripts/deploy.sh docker\"'", "test:build": "node scripts/test-build.js"}, "keywords": ["web-scraping", "rental-properties", "cloudflare-bypass", "puppeteer", "crawler"], "author": "", "license": "ISC", "engines": {"node": ">=14.0.0"}, "dependencies": {"axios": "^1.6.0", "chalk": "^4.1.2", "dotenv": "^16.3.1", "fs-extra": "^11.3.0", "mime-types": "^3.0.1", "node-cron": "^3.0.3", "puppeteer": "^24.10.1", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-adblocker": "^2.13.6", "puppeteer-extra-plugin-stealth": "^2.11.2"}}