{"name": "chotot-house-rent", "version": "1.0.0", "main": "index.js", "description": "A web crawler for rental property data from Cloudflare-protected sites", "scripts": {"start": "node app.js", "start-cli": "node src/cli.js start", "crawl": "node src/cli.js crawl", "resume": "node src/cli.js resume", "sessions": "node src/cli.js sessions", "session": "node src/cli.js session", "delete-session": "node src/cli.js delete-session", "test-config": "node src/cli.js test", "setup": "node scripts/setup.js", "analyze": "node scripts/analyze.js", "analyze-latest": "node scripts/analyze.js latest", "stats": "node scripts/analyze.js stats", "view": "node scripts/view-data.js", "view-latest": "node scripts/view-data.js latest", "serve": "node scripts/view-data.js serve", "dev": "node index.js", "test": "node test-system.js", "test-original": "echo \"Error: no test specified\" && exit 1", "docker:build": "docker build -t chotot-house-rent-crawler .", "docker:run": "docker-compose up -d", "docker:stop": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:restart": "docker-compose restart", "production:start": "NODE_ENV=production node app.js", "production:setup": "npm ci --only=production && npm run setup", "health-check": "node -e \"console.log('Health check passed')\"", "backup:data": "tar -czf backup-$(date +%Y%m%d-%H%M%S).tar.gz data/ output/ sessions/", "clean:logs": "find logs/ -name '*.log' -mtime +7 -delete 2>/dev/null || true", "clean:old-data": "find output/ -name '*.json' -mtime +30 -delete 2>/dev/null || true", "transfer": "./scripts/transfer.sh", "transfer:dry-run": "./scripts/transfer.sh --dry-run", "deploy:full": "./scripts/transfer.sh && echo 'Files transferred. Now run: ssh user@server \"cd /usr/src/app && sudo ./scripts/deploy.sh docker\"'", "test:build": "node scripts/test-build.js"}, "keywords": ["web-scraping", "rental-properties", "cloudflare-bypass", "puppeteer", "crawler"], "author": "", "license": "ISC", "engines": {"node": ">=14.0.0"}, "dependencies": {"axios": "^1.6.0", "chalk": "^4.1.2", "dotenv": "^16.3.1", "fs-extra": "^11.3.0", "mime-types": "^3.0.1", "node-cron": "^3.0.3", "puppeteer": "^24.10.1", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-adblocker": "^2.13.6", "puppeteer-extra-plugin-stealth": "^2.11.2"}}