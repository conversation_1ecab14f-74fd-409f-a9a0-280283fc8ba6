# Migration Guide: Project Restructure

This guide helps you migrate from the old project structure to the new, more organized structure.

## 🎯 What Changed

### Major Structural Changes

1. **Single Entry Point**: `app/index.js` replaces both `app.js` and `index.js`
2. **Organized Directories**: Code moved to logical directories (`app/`, `config/`, `docs/`, etc.)
3. **Configuration Management**: Centralized config system with environment support
4. **Documentation Organization**: All docs moved to `docs/` directory
5. **Deployment Organization**: Docker and deployment files moved to `deployment/`

### New Directory Structure

```
OLD STRUCTURE → NEW STRUCTURE
├── app.js                    → app/index.js (unified entry point)
├── index.js                  → app/index.js (unified entry point)
├── src/crawler.js           → app/core/crawler.js
├── src/scheduled-crawler.js → app/core/scheduled-crawler.js
├── src/cli.js               → app/cli/index.js
├── src/config.js            → config/default.js
├── src/extractors/          → app/extractors/
├── src/services/            → app/services/
├── src/utils/               → app/utils/
├── scripts/                 → scripts/ (unchanged)
├── Dockerfile               → deployment/docker/Dockerfile
├── docker-compose.yml       → deployment/docker/docker-compose.yml
├── *.md files               → docs/ (except README.md)
└── test-*.js                → tests/
```

## 🚀 Migration Steps

### Step 1: Update Your Commands

**Old Commands:**
```bash
node app.js                    # Scheduled mode
node index.js                  # Single crawl
node src/cli.js start          # CLI mode
```

**New Commands:**
```bash
npm start                      # Single crawl (default)
npm run start-scheduled        # Scheduled mode
npm run crawl                  # CLI crawl
```

### Step 2: Update Docker Usage

**Old Docker Commands:**
```bash
docker build -t chotot-house-rent-crawler .
docker-compose up -d
```

**New Docker Commands:**
```bash
npm run docker:build
npm run docker:run
```

### Step 3: Configuration Files

**No Changes Required** for these files:
- `proxy.config.js` (stays in root)
- `captcha.config.js` (stays in root)
- `urls.json` (stays in root)
- `.env` (stays in root)

**New Configuration Features:**
- Environment-specific configs in `config/`
- Better environment variable support
- Centralized configuration loading

### Step 4: Update Custom Scripts

If you have custom scripts that import project files:

**Old Imports:**
```javascript
const RentalCrawler = require('./src/crawler');
const config = require('./src/config');
```

**New Imports:**
```javascript
const RentalCrawler = require('./app/core/crawler');
const config = require('./config');
```

## 📋 Compatibility Checklist

### ✅ What Still Works (No Changes Needed)

- [x] All npm scripts (`npm start`, `npm run crawl`, etc.)
- [x] Environment variables and `.env` file
- [x] External config files (`proxy.config.js`, `captcha.config.js`)
- [x] Data directories (`output/`, `sessions/`, `data/`)
- [x] All existing functionality and features

### ⚠️ What Requires Updates

- [ ] Custom scripts that import project modules
- [ ] Docker build commands (use npm scripts instead)
- [ ] Direct node commands (use npm scripts instead)
- [ ] Documentation links (now in `docs/` directory)

## 🔧 Troubleshooting

### Common Issues and Solutions

**Issue: "Cannot find module" errors**
```bash
Error: Cannot find module './src/crawler'
```
**Solution:** Update import paths to new structure:
```javascript
// Old
const RentalCrawler = require('./src/crawler');
// New
const RentalCrawler = require('./app/core/crawler');
```

**Issue: Docker build fails**
```bash
Error: COPY failed: stat /var/lib/docker/tmp/docker-builder/app.js: no such file
```
**Solution:** Use the updated Docker commands:
```bash
npm run docker:build
npm run docker:run
```

**Issue: Configuration not loading**
```bash
Error: No proxy configuration found
```
**Solution:** Ensure external config files are still in the root directory:
- `proxy.config.js` → root directory
- `captcha.config.js` → root directory

## 🎉 Benefits of New Structure

### For Developers
- **Clearer Organization**: Logical separation of concerns
- **Better Maintainability**: Easier to find and modify code
- **Improved Testing**: Dedicated test directory
- **Enhanced Documentation**: Organized docs with clear navigation

### For Deployment
- **Simplified Entry Point**: Single command for all modes
- **Better Configuration**: Environment-specific settings
- **Organized Deployment**: Dedicated deployment directory
- **Improved Docker**: Better build context and organization

### For Users
- **Same Commands**: All npm scripts work the same
- **Better Documentation**: Clearer guides and organization
- **Easier Setup**: Improved configuration management
- **Enhanced Features**: Better error handling and logging

## 📞 Need Help?

If you encounter issues during migration:

1. **Check the Troubleshooting section** above
2. **Review the documentation** in `docs/`
3. **Verify your npm scripts** are up to date
4. **Ensure external configs** are in the right location

## 🔄 Rollback Plan

If you need to rollback to the old structure:

1. Keep a backup of your working directory
2. The old files are preserved (not deleted)
3. You can continue using direct node commands if needed
4. All data and configuration files remain unchanged

## ✨ Next Steps

After migration:
1. Test your setup with `npm run test-config`
2. Try a test crawl with `npm start`
3. Review the new documentation in `docs/`
4. Explore new configuration options in `config/`
