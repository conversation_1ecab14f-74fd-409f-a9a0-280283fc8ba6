/**
 * PM2 Ecosystem Configuration for Chotot House Rent Crawler
 * 
 * This file configures PM2 process manager for production deployment
 * Run with: pm2 start ecosystem.config.js
 */

module.exports = {
  apps: [
    {
      // Main application
      name: 'chotot-crawler',
      script: 'app.js',
      cwd: '/usr/src/app',
      
      // Environment
      env: {
        NODE_ENV: 'development',
        PORT: 3000,
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000,
      },

      // Process management
      instances: 1, // Single instance for crawler to avoid conflicts
      exec_mode: 'fork', // Use fork mode for single instance
      
      // Auto restart configuration
      autorestart: true,
      watch: false, // Don't watch files in production
      max_memory_restart: '2G',
      
      // Restart policy
      restart_delay: 5000, // Wait 5 seconds before restart
      max_restarts: 10, // Maximum restarts within min_uptime
      min_uptime: '10s', // Minimum uptime before considering restart
      
      // Logging
      log_file: './logs/pm2-combined.log',
      out_file: './logs/pm2-out.log',
      error_file: './logs/pm2-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Advanced settings
      kill_timeout: 5000, // Time to wait before force killing
      listen_timeout: 3000, // Time to wait for app to listen
      
      // Health monitoring
      health_check_grace_period: 3000,
      
      // Node.js specific
      node_args: [
        '--max-old-space-size=2048', // Increase memory limit
        '--optimize-for-size', // Optimize for memory usage
      ],
      
      // Environment variables (will be overridden by .env file)
      env_file: '.env',
      
      // Cron restart (optional - restart daily at 3 AM)
      cron_restart: '0 3 * * *',
      
      // Source map support
      source_map_support: false,
      
      // Disable automatic restart on file changes
      ignore_watch: [
        'node_modules',
        'logs',
        'output',
        'sessions',
        'data',
        '.git'
      ],
      
      // Process title
      name: 'chotot-house-rent-crawler',
      
      // User and group (uncomment if running as specific user)
      // user: 'crawler',
      // group: 'crawler',
    },
    
    // Optional: Web interface for viewing data
    {
      name: 'chotot-viewer',
      script: 'scripts/view-data.js',
      args: 'serve',
      cwd: '/usr/src/app',
      
      // Environment
      env: {
        NODE_ENV: 'development',
        PORT: 3001,
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3001,
      },
      
      // Process management
      instances: 1,
      exec_mode: 'fork',
      
      // Auto restart
      autorestart: true,
      watch: false,
      max_memory_restart: '512M',
      
      // Logging
      log_file: './logs/pm2-viewer-combined.log',
      out_file: './logs/pm2-viewer-out.log',
      error_file: './logs/pm2-viewer-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // This service is optional, so don't restart as aggressively
      max_restarts: 5,
      min_uptime: '30s',
      restart_delay: 10000,
      
      // Disable by default (can be enabled manually)
      disabled: true,
    }
  ],

  // Deployment configuration (optional)
  deploy: {
    production: {
      user: 'deploy',
      host: ['your-server.com'],
      ref: 'origin/main',
      repo: 'https://github.com/your-username/chotot-house-rent.git',
      path: '/var/www/chotot-crawler',
      'post-deploy': 'npm install --production && pm2 reload ecosystem.config.js --env production',
      'pre-setup': 'apt-get update && apt-get install -y git nodejs npm',
      'post-setup': 'npm install --production && pm2 start ecosystem.config.js --env production',
      env: {
        NODE_ENV: 'production'
      }
    }
  }
};
