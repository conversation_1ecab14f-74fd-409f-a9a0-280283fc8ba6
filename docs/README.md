# Documentation

This directory contains comprehensive documentation for the Rental Property Crawler project.

## 📚 Documentation Index

### Getting Started
- [Main README](../README.md) - Project overview and quick start guide
- [Features](FEATURES.md) - Detailed feature list and capabilities
- [Project Summary](PROJECT_SUMMARY.md) - Complete project overview

### Setup and Configuration
- [Chotot Guide](CHOTOT_GUIDE.md) - Specific guide for crawling Chotot/Nhatot
- [Slack Integration](README-SLACK.md) - Setting up Slack notifications

### Deployment
- [Deployment Guide](DEPLOYMENT.md) - Complete deployment instructions
- [Deployment Summary](DEPLOYMENT_SUMMARY.md) - Quick deployment reference

## 📁 Project Structure

After restructuring, the project follows this organization:

```
chotot-house-rent/
├── README.md                    # Main project documentation
├── package.json                 # Project dependencies and scripts
├── .env.example                # Environment configuration template
├── app/                        # Main application code
│   ├── index.js               # Single entry point
│   ├── core/                  # Core application logic
│   │   ├── crawler.js         # Main crawler engine
│   │   └── scheduled-crawler.js # Scheduled crawler with notifications
│   ├── extractors/            # Site-specific data extractors
│   │   ├── chotot.js         # Chotot/Nhatot extractor
│   │   └── demo.js           # Demo site extractor
│   ├── services/              # Business services
│   │   ├── database.js       # Data persistence service
│   │   └── slack.js          # Slack notification service
│   ├── utils/                 # Utility functions
│   │   ├── stealth.js        # Browser stealth utilities
│   │   ├── proxy.js          # Proxy management
│   │   ├── captcha.js        # CAPTCHA handling
│   │   └── session.js        # Session management
│   └── cli/                   # Command line interface
│       └── index.js          # CLI commands and options
├── config/                     # Configuration management
│   ├── index.js              # Configuration loader
│   ├── default.js            # Default configuration
│   ├── production.js         # Production overrides
│   └── examples/             # Example configuration files
├── scripts/                    # Utility scripts
├── docs/                      # Documentation
├── tests/                     # Test files
├── deployment/                # Deployment configurations
│   ├── docker/               # Docker configurations
│   └── systemd/              # SystemD service files
├── data/                      # Runtime data
├── output/                    # Crawled data output
├── sessions/                  # Session files
└── logs/                      # Application logs
```

## 🚀 Key Improvements

### 1. **Single Entry Point**
- `app/index.js` serves as the unified entry point
- Automatically detects mode (CLI, scheduled, or single crawl)
- Simplified startup process

### 2. **Organized Configuration**
- Centralized configuration management in `config/`
- Environment-specific configurations
- External config file support (proxy, captcha)
- Environment variable overrides

### 3. **Clear Separation of Concerns**
- Core logic in `app/core/`
- Services in `app/services/`
- Utilities in `app/utils/`
- CLI commands in `app/cli/`

### 4. **Better Documentation Structure**
- All documentation in `docs/` directory
- Clear documentation index
- Deployment guides separated

### 5. **Deployment Organization**
- Docker files in `deployment/docker/`
- SystemD services in `deployment/systemd/`
- PM2 configuration in `deployment/`

## 📖 Quick Navigation

- **New to the project?** Start with the [Main README](../README.md)
- **Setting up Chotot crawling?** See [Chotot Guide](CHOTOT_GUIDE.md)
- **Want Slack notifications?** Check [Slack Integration](README-SLACK.md)
- **Ready to deploy?** Follow the [Deployment Guide](DEPLOYMENT.md)
- **Need all features?** Review [Features](FEATURES.md)

## 🔄 Migration from Old Structure

If you're upgrading from the previous structure:

1. **Entry Points**: Use `npm start` or `node app/index.js` instead of `node app.js`
2. **Configuration**: External configs (proxy.config.js, captcha.config.js) remain in root
3. **Scripts**: All npm scripts remain the same
4. **Data**: All data directories (output/, sessions/, data/) remain unchanged

## 📝 Contributing

When adding new documentation:
1. Place it in the appropriate subdirectory
2. Update this README index
3. Link from relevant existing documentation
4. Follow the established documentation style
