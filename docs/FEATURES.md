# Rental Property Crawler - Features

## 🚀 Core Features

### Web Scraping Engine
- **Stealth Browser**: Advanced Puppeteer configuration with stealth plugins
- **Cloudflare Bypass**: Multiple techniques to bypass bot detection
- **Human-like Behavior**: Random delays, scrolling, and realistic interactions
- **Retry Logic**: Robust error handling with configurable retry attempts

### Multi-Site Support
- **Modular Extractors**: Easy to add new sites with custom extraction logic
- **Configurable Selectors**: CSS selectors for different site structures
- **Demo Site**: Working example with quotes.toscrape.com
- **Chotot Ready**: Pre-configured for Vietnamese rental site (requires proxy)

### Data Management
- **JSON Output**: Structured data with metadata
- **CSV Export**: Spreadsheet-compatible format
- **Session Management**: Resume interrupted crawling sessions
- **Data Analysis**: Built-in analytics for crawled data

## 🛡️ Anti-Detection Features

### Browser Stealth
- **WebDriver Removal**: Eliminates automation detection markers
- **Realistic Headers**: Human-like browser headers and user agents
- **Geolocation**: Set to Vietnam for local content access
- **Plugin Simulation**: Mimics real browser plugins and capabilities

### Proxy Support
- **Multiple Proxies**: Rotate through proxy lists
- **Authentication**: Username/password proxy support
- **Validation**: Test proxy connectivity before use
- **Failure Handling**: Automatic proxy switching on failures

### CAPTCHA Handling
- **Service Integration**: 2Captcha and Anti-Captcha support
- **Manual Solving**: Option for human intervention
- **Challenge Detection**: Automatic CAPTCHA type identification
- **Cloudflare Turnstile**: Specialized handling for Cloudflare challenges

## 📊 Session & Analytics

### Session Management
- **Auto-Save**: Periodic session state saving
- **Resume Capability**: Continue from where you left off
- **Progress Tracking**: Monitor crawled URLs and extracted properties
- **Error Logging**: Track failed URLs and retry attempts

### Data Analysis
- **Price Analysis**: Min, max, average price calculations
- **Location Insights**: Top locations and distribution
- **Area Statistics**: Property size analysis
- **Source Tracking**: Multi-site data aggregation

### Monitoring
- **Real-time Progress**: Live crawling status updates
- **Session Statistics**: Detailed session information
- **Performance Metrics**: Speed and success rate tracking
- **Export Tools**: Data export in multiple formats

## 🔧 Configuration & Customization

### Flexible Configuration
- **External Config Files**: Separate proxy and CAPTCHA configurations
- **Environment Variables**: Support for .env files
- **Command Line Options**: Runtime parameter overrides
- **Site-Specific Settings**: Custom settings per target site

### Extensibility
- **Plugin Architecture**: Easy to add new extractors
- **Custom Selectors**: Adaptable to different site structures
- **Configurable Delays**: Adjust crawling speed and behavior
- **Output Formats**: Multiple data export options

## 🖥️ Command Line Interface

### User-Friendly Commands
```bash
npm start                    # Start new crawling session
npm run resume <session>     # Resume existing session
npm run sessions             # List all sessions
npm run test-config          # Test configuration
npm run analyze-latest       # Analyze latest data
npm run stats               # Show statistics
npm run setup               # Initial setup
```

### Advanced Options
```bash
npm run crawl -- --no-proxy        # Disable proxy
npm run crawl -- --max-pages 5     # Limit pages
npm run crawl -- --no-headless     # Show browser
npm run crawl -- --enable-captcha  # Enable CAPTCHA solving
```

## 📁 Project Structure

```
chotot-house-rent/
├── src/
│   ├── crawler.js           # Main crawler class
│   ├── cli.js              # Command line interface
│   ├── config.js           # Configuration management
│   ├── extractors/         # Site-specific extractors
│   │   ├── chotot.js       # Chotot extractor
│   │   └── demo.js         # Demo extractor
│   └── utils/              # Utility modules
│       ├── stealth.js      # Browser stealth utilities
│       ├── proxy.js        # Proxy management
│       ├── captcha.js      # CAPTCHA handling
│       └── session.js      # Session management
├── scripts/
│   ├── setup.js            # Setup script
│   └── analyze.js          # Data analysis
├── output/                 # Crawled data
├── sessions/               # Session files
├── data/                   # Configuration data
├── urls.json              # Target URLs
├── proxy.config.js        # Proxy settings
├── captcha.config.js      # CAPTCHA settings
└── README.md              # Documentation
```

## 🎯 Use Cases

### Real Estate Research
- **Market Analysis**: Gather rental property data for market research
- **Price Monitoring**: Track rental price trends over time
- **Location Analysis**: Identify popular rental areas
- **Inventory Tracking**: Monitor available properties

### Business Intelligence
- **Competitive Analysis**: Monitor competitor listings
- **Market Trends**: Analyze rental market patterns
- **Data Aggregation**: Combine data from multiple sources
- **Automated Reporting**: Generate regular market reports

### Academic Research
- **Housing Studies**: Research rental market dynamics
- **Urban Planning**: Analyze housing distribution patterns
- **Economic Research**: Study rental price correlations
- **Data Science Projects**: Large-scale property data analysis

## 🔒 Legal & Ethical Considerations

### Responsible Crawling
- **Rate Limiting**: Configurable delays to avoid server overload
- **Robots.txt Compliance**: Respect website crawling policies
- **Terms of Service**: Ensure compliance with site terms
- **Data Privacy**: Handle personal data responsibly

### Best Practices
- **Conservative Delays**: Use reasonable crawling speeds
- **Error Handling**: Graceful failure and retry mechanisms
- **Resource Management**: Efficient memory and CPU usage
- **Logging**: Comprehensive activity logging for transparency

## 🚀 Getting Started

1. **Installation**: `npm install`
2. **Setup**: `npm run setup`
3. **Configuration**: Update proxy and CAPTCHA settings
4. **Test**: `npm run test-config`
5. **Crawl**: `npm start`
6. **Analyze**: `npm run analyze-latest`

## 📈 Performance

### Optimizations
- **Concurrent Processing**: Efficient page processing
- **Memory Management**: Optimized for long-running sessions
- **Network Efficiency**: Minimal bandwidth usage
- **Resource Cleanup**: Proper browser and session cleanup

### Scalability
- **Session Resumption**: Handle large crawling jobs
- **Proxy Rotation**: Scale with multiple proxy sources
- **Modular Design**: Easy to extend and customize
- **Error Recovery**: Robust failure handling

This crawler provides a comprehensive solution for rental property data extraction with enterprise-grade features for reliability, stealth, and data management.
