# 🏠 Rental Property Crawler with Slack Notifications

An automated rental property crawler that monitors Chotot for new listings and sends beautiful notifications to Slack every 4 hours.

## ✨ Features

- **🔄 Automated Crawling**: Runs every 4 hours automatically
- **📱 Slack Notifications**: Beautiful, rich notifications with property images
- **🆕 New Property Detection**: Only sends new properties, avoiding spam
- **📄 Smart Pagination**: Automatically crawls multiple pages when all items are new
- **💾 Data Persistence**: Tracks seen properties to avoid duplicates
- **🖼️ Image Support**: Displays property images in Slack messages
- **🧹 Auto Cleanup**: Removes old property data (30+ days)
- **❌ Error Handling**: Sends error notifications to Slack

## 🚀 Quick Start

### 1. Setup Environment

Create a `.env` file with your Slack webhook URL:

```bash
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Test Slack Integration

```bash
npm test
```

This will send test messages to your Slack channel to verify the integration works.

### 4. Start the Scheduled Crawler

```bash
npm start
```

The crawler will:

1. Run an initial crawl and send all found properties to Slack
2. Schedule automatic crawls every 4 hours
3. Send only new properties in subsequent runs

## 📋 How It Works

### Initial Run

- Crawls the first page of rental properties
- Sends all found properties to Slack as "Initial Results"
- Saves all properties to the database as "seen"

### Scheduled Runs (Every 4 Hours)

- Starts crawling from page 1
- If all properties on page 1 are new → continues to page 2, 3, etc.
- If some properties are already seen → stops crawling
- Sends only new properties to Slack

### Smart Pagination

The system automatically adds `&page=2`, `&page=3`, etc. to the URL:

- Page 1: `https://www.nhatot.com/thue-bat-dong-san-quan-7-tp-ho-chi-minh?price=5000000-8000000`
- Page 2: `https://www.nhatot.com/thue-bat-dong-san-quan-7-tp-ho-chi-minh?price=5000000-8000000&page=2`
- Page 3: `https://www.nhatot.com/thue-bat-dong-san-quan-7-tp-ho-chi-minh?price=5000000-8000000&page=3`

## 📱 Slack Message Examples

### Initial Results

```
🏠 Rental Property Crawler - Initial Results

📊 Summary:
• Total properties found: 21
• Properties with images: 6
• Crawled at: 13/06/2025, 22:04:57

[Property cards with images, prices, locations, and "Xem chi tiết" buttons]
```

### New Properties Found

```
🆕 New Rental Properties Found!

📊 New Properties:
• Total new: 3
• With images: 2
• Found at: 13/06/2025, 22:04:57

[New property cards only]
```

### No New Properties

```
🔄 Rental Property Update

✅ No new properties found
Last checked: 13/06/2025, 22:04:57
```

### Error Notifications

```
❌ Crawler Error

Error occurred: Crawl cycle failed
```

[Error details]
Time: 13/06/2025, 22:04:57

```

## 🗂️ File Structure

```

├── app.js # Main application entry point
├── src/
│ ├── scheduled-crawler.js # Main scheduled crawler logic
│ ├── services/
│ │ ├── slack.js # Slack notification service
│ │ └── database.js # Data persistence service
│ └── crawler.js # Original crawler (reused)
├── data/ # Database files (auto-created)
│ ├── seen_properties.json # Tracks seen properties
│ └── last_crawl.json # Last crawl metadata
└── test-slack.js # Test script for Slack integration

````

## 🛠️ Configuration

### Crawler Settings
Edit `src/scheduled-crawler.js` to modify:
- **Base URL**: Change the target website URL
- **Schedule**: Modify the cron pattern (default: every 4 hours)
- **Max Pages**: Limit how many pages to crawl (default: 5)

### Slack Message Batching
- **All properties are sent** - no limits on total number
- Properties are sent in batches of 10 per message (configurable)
- 1-second delay between batches to avoid rate limiting
- Images are automatically filtered and validated
- Messages include property details, images, and action buttons
- Batch progress is shown in multi-batch scenarios

#### Customizing Batch Settings
To modify batch behavior, edit `src/services/slack.js`:
```javascript
class SlackService {
  constructor() {
    // ...
    this.batchSize = 10; // Properties per message (change this number)
    this.delayBetweenBatches = 1000; // Delay in milliseconds (1 second)
  }
}
```

## 📊 Database

The system uses simple JSON files for data persistence:

### `data/seen_properties.json`
```json
{
  "properties": {
    "property_key": {
      "id": "chotot_123",
      "title": "Property Title",
      "price": "6,2",
      "location": "Phường Tân Quy",
      "url": "https://...",
      "firstSeen": "2025-06-13T15:04:27.324Z",
      "source": "nhatot.com"
    }
  },
  "lastUpdated": "2025-06-13T15:04:27.324Z"
}
````

### `data/last_crawl.json`

```json
{
  "lastCrawlTime": "2025-06-13T15:04:27.324Z",
  "lastPageCrawled": 2,
  "totalPropertiesFound": 42,
  "newPropertiesFound": 3
}
```

## 🔧 Commands

```bash
# Start the scheduled crawler
npm start

# Test Slack integration
npm test

# Run original crawler once
npm run crawl

# Start original CLI
npm run start-cli
```

## 🚨 Error Handling

The system includes comprehensive error handling:

- **Slack Errors**: Logged to console, crawler continues
- **Crawl Errors**: Sent to Slack, system retries next cycle
- **Database Errors**: Graceful fallbacks, no data loss
- **Process Errors**: Clean shutdown, error notifications

## 🧹 Maintenance

### Auto Cleanup

- Properties older than 30 days are automatically removed
- Runs during each crawl cycle
- Prevents database from growing indefinitely

### Manual Cleanup

```bash
# Clear all seen properties (start fresh)
rm data/seen_properties.json

# Reset crawl history
rm data/last_crawl.json
```

## 🎯 Stopping the Crawler

Press `Ctrl+C` to gracefully stop the scheduled crawler.

## 🔍 Troubleshooting

### No Slack Messages

1. Check your `.env` file has the correct webhook URL
2. Test with `npm test`
3. Verify webhook URL in Slack settings

### No New Properties Found

1. Check if properties are already in `data/seen_properties.json`
2. Clear the database to start fresh
3. Verify the target URL is accessible

### Crawler Errors

1. Check console output for detailed error messages
2. Error notifications are sent to Slack automatically
3. System will retry on next scheduled run

---

🎉 **Happy Property Hunting!** The crawler will keep you updated with the latest rental opportunities in Ho Chi Minh City!
