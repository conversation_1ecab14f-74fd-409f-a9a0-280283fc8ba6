#!/usr/bin/env node

/**
 * Build Test Script for Chotot House Rent Crawler
 * This script validates the build configuration and dependencies
 */

const fs = require('fs');
const path = require('path');

// Colors for output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(level, message) {
  const timestamp = new Date().toISOString();
  const color = colors[level] || colors.reset;
  console.log(`${color}[${level.toUpperCase()}]${colors.reset} ${timestamp} - ${message}`);
}

function logInfo(message) { log('blue', message); }
function logSuccess(message) { log('green', message); }
function logWarning(message) { log('yellow', message); }
function logError(message) { log('red', message); }

// Test functions
async function testNodeVersion() {
  logInfo('Testing Node.js version...');
  const version = process.version;
  const majorVersion = parseInt(version.slice(1).split('.')[0]);
  
  if (majorVersion >= 18) {
    logSuccess(`Node.js version ${version} is supported`);
    return true;
  } else {
    logError(`Node.js version ${version} is not supported. Minimum required: 18.x`);
    return false;
  }
}

async function testDependencies() {
  logInfo('Testing dependencies...');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const dependencies = Object.keys(packageJson.dependencies || {});
    
    logInfo(`Found ${dependencies.length} dependencies`);
    
    // Test critical dependencies
    const criticalDeps = ['puppeteer', 'axios', 'node-cron', 'dotenv'];
    let allPresent = true;
    
    for (const dep of criticalDeps) {
      if (dependencies.includes(dep)) {
        logSuccess(`✓ ${dep}`);
      } else {
        logError(`✗ Missing critical dependency: ${dep}`);
        allPresent = false;
      }
    }
    
    return allPresent;
  } catch (error) {
    logError(`Failed to read package.json: ${error.message}`);
    return false;
  }
}

async function testConfiguration() {
  logInfo('Testing configuration files...');
  
  const configFiles = [
    { file: 'src/config.js', required: true },
    { file: 'src/config.production.js', required: true },
    { file: 'src/config.loader.js', required: true },
    { file: '.env.example', required: true },
    { file: 'urls.example.json', required: true },
    { file: 'proxy.config.example.js', required: true },
    { file: 'captcha.config.example.js', required: true }
  ];
  
  let allPresent = true;
  
  for (const { file, required } of configFiles) {
    if (fs.existsSync(file)) {
      logSuccess(`✓ ${file}`);
    } else if (required) {
      logError(`✗ Missing required file: ${file}`);
      allPresent = false;
    } else {
      logWarning(`⚠ Optional file missing: ${file}`);
    }
  }
  
  return allPresent;
}

async function testConfigurationLoading() {
  logInfo('Testing configuration loading...');
  
  try {
    // Test development config
    process.env.NODE_ENV = 'development';
    delete require.cache[require.resolve('../src/config.loader')];
    const devConfig = require('../src/config.loader');
    
    if (devConfig && devConfig.environment && devConfig.environment.name === 'development') {
      logSuccess('✓ Development configuration loads correctly');
    } else {
      logError('✗ Development configuration failed to load');
      return false;
    }
    
    // Test production config
    process.env.NODE_ENV = 'production';
    delete require.cache[require.resolve('../src/config.loader')];
    const prodConfig = require('../src/config.loader');
    
    if (prodConfig && prodConfig.environment && prodConfig.environment.name === 'production') {
      logSuccess('✓ Production configuration loads correctly');
    } else {
      logError('✗ Production configuration failed to load');
      return false;
    }
    
    return true;
  } catch (error) {
    logError(`Configuration loading failed: ${error.message}`);
    return false;
  }
}

async function testDockerFiles() {
  logInfo('Testing Docker configuration...');
  
  const dockerFiles = [
    'Dockerfile',
    'docker-compose.yml',
    '.dockerignore'
  ];
  
  let allPresent = true;
  
  for (const file of dockerFiles) {
    if (fs.existsSync(file)) {
      logSuccess(`✓ ${file}`);
    } else {
      logError(`✗ Missing Docker file: ${file}`);
      allPresent = false;
    }
  }
  
  return allPresent;
}

async function testProcessManagement() {
  logInfo('Testing process management files...');
  
  const pmFiles = [
    'ecosystem.config.js',
    'chotot-crawler.service',
    'scripts/process-manager.sh'
  ];
  
  let allPresent = true;
  
  for (const file of pmFiles) {
    if (fs.existsSync(file)) {
      logSuccess(`✓ ${file}`);
      
      // Check if shell scripts are executable
      if (file.endsWith('.sh')) {
        try {
          const stats = fs.statSync(file);
          if (stats.mode & parseInt('111', 8)) {
            logSuccess(`✓ ${file} is executable`);
          } else {
            logWarning(`⚠ ${file} is not executable`);
          }
        } catch (error) {
          logWarning(`⚠ Could not check permissions for ${file}`);
        }
      }
    } else {
      logError(`✗ Missing process management file: ${file}`);
      allPresent = false;
    }
  }
  
  return allPresent;
}

async function testDeploymentScripts() {
  logInfo('Testing deployment scripts...');
  
  const scripts = [
    'scripts/deploy.sh',
    'scripts/monitor.sh'
  ];
  
  let allPresent = true;
  
  for (const script of scripts) {
    if (fs.existsSync(script)) {
      logSuccess(`✓ ${script}`);
      
      // Check if executable
      try {
        const stats = fs.statSync(script);
        if (stats.mode & parseInt('111', 8)) {
          logSuccess(`✓ ${script} is executable`);
        } else {
          logWarning(`⚠ ${script} is not executable`);
        }
      } catch (error) {
        logWarning(`⚠ Could not check permissions for ${script}`);
      }
    } else {
      logError(`✗ Missing deployment script: ${script}`);
      allPresent = false;
    }
  }
  
  return allPresent;
}

async function testDirectoryStructure() {
  logInfo('Testing directory structure...');
  
  const directories = [
    'src',
    'scripts',
    'data',
    'output',
    'logs',
    'sessions'
  ];
  
  let allPresent = true;
  
  for (const dir of directories) {
    if (fs.existsSync(dir) && fs.statSync(dir).isDirectory()) {
      logSuccess(`✓ ${dir}/`);
    } else {
      // Create missing directories
      try {
        fs.mkdirSync(dir, { recursive: true });
        logSuccess(`✓ Created ${dir}/`);
      } catch (error) {
        logError(`✗ Could not create directory: ${dir}`);
        allPresent = false;
      }
    }
  }
  
  return allPresent;
}

async function testPackageScripts() {
  logInfo('Testing package.json scripts...');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const scripts = packageJson.scripts || {};
    
    const requiredScripts = [
      'start',
      'health-check',
      'docker:build',
      'docker:run',
      'production:start'
    ];
    
    let allPresent = true;
    
    for (const script of requiredScripts) {
      if (scripts[script]) {
        logSuccess(`✓ npm run ${script}`);
      } else {
        logError(`✗ Missing script: ${script}`);
        allPresent = false;
      }
    }
    
    return allPresent;
  } catch (error) {
    logError(`Failed to read package.json scripts: ${error.message}`);
    return false;
  }
}

// Main test runner
async function runTests() {
  logInfo('Starting build validation tests...');
  console.log('='.repeat(60));
  
  const tests = [
    { name: 'Node.js Version', fn: testNodeVersion },
    { name: 'Dependencies', fn: testDependencies },
    { name: 'Configuration Files', fn: testConfiguration },
    { name: 'Configuration Loading', fn: testConfigurationLoading },
    { name: 'Docker Files', fn: testDockerFiles },
    { name: 'Process Management', fn: testProcessManagement },
    { name: 'Deployment Scripts', fn: testDeploymentScripts },
    { name: 'Directory Structure', fn: testDirectoryStructure },
    { name: 'Package Scripts', fn: testPackageScripts }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    console.log('\n' + '-'.repeat(40));
    logInfo(`Running test: ${test.name}`);
    
    try {
      const result = await test.fn();
      if (result) {
        logSuccess(`✅ ${test.name} - PASSED`);
        passed++;
      } else {
        logError(`❌ ${test.name} - FAILED`);
        failed++;
      }
    } catch (error) {
      logError(`❌ ${test.name} - ERROR: ${error.message}`);
      failed++;
    }
  }
  
  console.log('\n' + '='.repeat(60));
  logInfo('Build validation completed');
  logInfo(`Tests passed: ${passed}`);
  logInfo(`Tests failed: ${failed}`);
  
  if (failed === 0) {
    logSuccess('🎉 All tests passed! Build is ready for deployment.');
    return true;
  } else {
    logError(`💥 ${failed} test(s) failed. Please fix the issues before deployment.`);
    return false;
  }
}

// Run tests if called directly
if (require.main === module) {
  runTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      logError(`Test runner failed: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { runTests };
