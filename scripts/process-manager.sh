#!/bin/bash

# Chotot House Rent Crawler Process Manager
# This script helps manage the crawler process using either PM2 or systemd

set -e

# Configuration
APP_NAME="chotot-crawler"
APP_DIR="/usr/src/app"
SERVICE_FILE="chotot-crawler.service"
PM2_CONFIG="ecosystem.config.js"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check if running as root
is_root() {
    [ "$EUID" -eq 0 ]
}

# PM2 functions
pm2_install() {
    log_info "Installing PM2..."
    npm install -g pm2
    log_success "PM2 installed successfully"
}

pm2_start() {
    log_info "Starting application with PM2..."
    cd "$APP_DIR"
    pm2 start "$PM2_CONFIG" --env production
    pm2 save
    log_success "Application started with PM2"
}

pm2_stop() {
    log_info "Stopping application with PM2..."
    pm2 stop "$APP_NAME" || true
    log_success "Application stopped"
}

pm2_restart() {
    log_info "Restarting application with PM2..."
    pm2 restart "$APP_NAME" || pm2_start
    log_success "Application restarted"
}

pm2_status() {
    pm2 status "$APP_NAME"
}

pm2_logs() {
    pm2 logs "$APP_NAME" --lines 50
}

pm2_setup_startup() {
    log_info "Setting up PM2 startup script..."
    pm2 startup
    pm2 save
    log_success "PM2 startup script configured"
}

# Systemd functions
systemd_install() {
    if ! is_root; then
        log_error "Root privileges required for systemd installation"
        exit 1
    fi
    
    log_info "Installing systemd service..."
    cp "$APP_DIR/$SERVICE_FILE" /etc/systemd/system/
    systemctl daemon-reload
    systemctl enable "$APP_NAME"
    log_success "Systemd service installed and enabled"
}

systemd_start() {
    if ! is_root; then
        log_error "Root privileges required for systemd operations"
        exit 1
    fi
    
    log_info "Starting application with systemd..."
    systemctl start "$APP_NAME"
    log_success "Application started with systemd"
}

systemd_stop() {
    if ! is_root; then
        log_error "Root privileges required for systemd operations"
        exit 1
    fi
    
    log_info "Stopping application with systemd..."
    systemctl stop "$APP_NAME"
    log_success "Application stopped"
}

systemd_restart() {
    if ! is_root; then
        log_error "Root privileges required for systemd operations"
        exit 1
    fi
    
    log_info "Restarting application with systemd..."
    systemctl restart "$APP_NAME"
    log_success "Application restarted"
}

systemd_status() {
    systemctl status "$APP_NAME"
}

systemd_logs() {
    journalctl -u "$APP_NAME" -n 50 -f
}

# Main functions
show_help() {
    echo "Chotot House Rent Crawler Process Manager"
    echo ""
    echo "Usage: $0 [MANAGER] [COMMAND]"
    echo ""
    echo "Managers:"
    echo "  pm2      Use PM2 process manager"
    echo "  systemd  Use systemd service manager"
    echo "  auto     Auto-detect available manager"
    echo ""
    echo "Commands:"
    echo "  install  Install and configure the process manager"
    echo "  start    Start the application"
    echo "  stop     Stop the application"
    echo "  restart  Restart the application"
    echo "  status   Show application status"
    echo "  logs     Show application logs"
    echo "  setup    Setup startup scripts (PM2 only)"
    echo ""
    echo "Examples:"
    echo "  $0 pm2 start"
    echo "  $0 systemd install"
    echo "  $0 auto status"
}

detect_manager() {
    if command_exists pm2; then
        echo "pm2"
    elif command_exists systemctl; then
        echo "systemd"
    else
        echo "none"
    fi
}

# Main script
main() {
    local manager="$1"
    local command="$2"
    
    if [ -z "$manager" ] || [ -z "$command" ]; then
        show_help
        exit 1
    fi
    
    # Auto-detect manager if requested
    if [ "$manager" = "auto" ]; then
        manager=$(detect_manager)
        if [ "$manager" = "none" ]; then
            log_error "No supported process manager found (PM2 or systemd)"
            exit 1
        fi
        log_info "Auto-detected manager: $manager"
    fi
    
    # Execute command based on manager
    case "$manager" in
        pm2)
            case "$command" in
                install) pm2_install ;;
                start) pm2_start ;;
                stop) pm2_stop ;;
                restart) pm2_restart ;;
                status) pm2_status ;;
                logs) pm2_logs ;;
                setup) pm2_setup_startup ;;
                *) log_error "Unknown command: $command"; show_help; exit 1 ;;
            esac
            ;;
        systemd)
            case "$command" in
                install) systemd_install ;;
                start) systemd_start ;;
                stop) systemd_stop ;;
                restart) systemd_restart ;;
                status) systemd_status ;;
                logs) systemd_logs ;;
                setup) log_warning "Setup not needed for systemd" ;;
                *) log_error "Unknown command: $command"; show_help; exit 1 ;;
            esac
            ;;
        *)
            log_error "Unknown manager: $manager"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
