#!/bin/bash

# Chotot House Rent Crawler Monitoring Script
# This script monitors the application and sends alerts if issues are detected

set -e

# Configuration
APP_NAME="chotot-house-rent-crawler"
APP_DIR="/usr/src/app"
LOG_FILE="/var/log/chotot-monitor.log"
SLACK_WEBHOOK_URL="${SLACK_WEBHOOK_URL:-}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [INFO] $1" >> "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [SUCCESS] $1" >> "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [WARNING] $1" >> "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [ERROR] $1" >> "$LOG_FILE"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Send Slack notification
send_slack_notification() {
    local message="$1"
    local color="${2:-warning}"
    
    if [ -z "$SLACK_WEBHOOK_URL" ]; then
        log_warning "Slack webhook URL not configured"
        return
    fi
    
    local payload=$(cat <<EOF
{
    "attachments": [
        {
            "color": "$color",
            "title": "🏠 Chotot Crawler Monitor Alert",
            "text": "$message",
            "footer": "$(hostname)",
            "ts": $(date +%s)
        }
    ]
}
EOF
)
    
    curl -X POST -H 'Content-type: application/json' \
        --data "$payload" \
        "$SLACK_WEBHOOK_URL" >/dev/null 2>&1 || log_warning "Failed to send Slack notification"
}

# Check if application is running
check_process() {
    log_info "Checking if application is running..."
    
    if command_exists docker; then
        # Check Docker container
        if docker ps --format "table {{.Names}}" | grep -q "$APP_NAME"; then
            log_success "Docker container is running"
            return 0
        else
            log_error "Docker container is not running"
            return 1
        fi
    elif command_exists pm2; then
        # Check PM2 process
        if pm2 list | grep -q "$APP_NAME.*online"; then
            log_success "PM2 process is running"
            return 0
        else
            log_error "PM2 process is not running"
            return 1
        fi
    else
        # Check regular process
        if pgrep -f "node.*app.js" > /dev/null; then
            log_success "Node.js process is running"
            return 0
        else
            log_error "Node.js process is not running"
            return 1
        fi
    fi
}

# Check system resources
check_resources() {
    log_info "Checking system resources..."
    
    # Check memory usage
    local memory_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    log_info "Memory usage: ${memory_usage}%"
    
    if (( $(echo "$memory_usage > 90" | bc -l) )); then
        log_warning "High memory usage: ${memory_usage}%"
        send_slack_notification "⚠️ High memory usage detected: ${memory_usage}%" "warning"
    fi
    
    # Check disk usage
    local disk_usage=$(df "$APP_DIR" | tail -1 | awk '{print $5}' | sed 's/%//')
    log_info "Disk usage: ${disk_usage}%"
    
    if [ "$disk_usage" -gt 85 ]; then
        log_warning "High disk usage: ${disk_usage}%"
        send_slack_notification "⚠️ High disk usage detected: ${disk_usage}%" "warning"
    fi
    
    # Check CPU load
    local cpu_load=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    log_info "CPU load: $cpu_load"
}

# Check log files for errors
check_logs() {
    log_info "Checking application logs for errors..."
    
    local error_count=0
    local log_files=("$APP_DIR/logs/pm2-error.log" "$APP_DIR/logs/crawler.log")
    
    for log_file in "${log_files[@]}"; do
        if [ -f "$log_file" ]; then
            # Check for errors in the last 10 minutes
            local recent_errors=$(find "$log_file" -mmin -10 -exec grep -i "error\|exception\|failed" {} \; 2>/dev/null | wc -l)
            error_count=$((error_count + recent_errors))
        fi
    done
    
    if [ "$error_count" -gt 0 ]; then
        log_warning "Found $error_count recent errors in logs"
        send_slack_notification "⚠️ Found $error_count recent errors in application logs" "warning"
    else
        log_success "No recent errors found in logs"
    fi
}

# Check data freshness
check_data_freshness() {
    log_info "Checking data freshness..."
    
    local latest_file=$(find "$APP_DIR/output" -name "*.json" -type f -printf '%T@ %p\n' 2>/dev/null | sort -n | tail -1 | cut -d' ' -f2-)
    
    if [ -n "$latest_file" ]; then
        local file_age=$(( $(date +%s) - $(stat -c %Y "$latest_file") ))
        local hours_old=$(( file_age / 3600 ))
        
        log_info "Latest data file is $hours_old hours old"
        
        if [ "$hours_old" -gt 6 ]; then
            log_warning "Data is stale (${hours_old} hours old)"
            send_slack_notification "⚠️ Data appears stale - latest file is ${hours_old} hours old" "warning"
        fi
    else
        log_warning "No data files found"
        send_slack_notification "⚠️ No data files found in output directory" "warning"
    fi
}

# Restart application if needed
restart_if_needed() {
    if ! check_process; then
        log_warning "Application is not running, attempting restart..."
        send_slack_notification "🔄 Application not running, attempting restart..." "warning"
        
        if command_exists docker; then
            docker-compose -f "$APP_DIR/docker-compose.yml" restart
        elif command_exists pm2; then
            pm2 restart "$APP_NAME"
        else
            log_error "Cannot restart application - no supported process manager found"
            send_slack_notification "❌ Cannot restart application - no supported process manager found" "danger"
            return 1
        fi
        
        # Wait and check again
        sleep 30
        if check_process; then
            log_success "Application restarted successfully"
            send_slack_notification "✅ Application restarted successfully" "good"
        else
            log_error "Failed to restart application"
            send_slack_notification "❌ Failed to restart application" "danger"
            return 1
        fi
    fi
}

# Cleanup old files
cleanup_old_files() {
    log_info "Cleaning up old files..."
    
    # Clean old log files (older than 7 days)
    find "$APP_DIR/logs" -name "*.log" -mtime +7 -delete 2>/dev/null || true
    
    # Clean old output files (older than 30 days)
    find "$APP_DIR/output" -name "*.json" -mtime +30 -delete 2>/dev/null || true
    
    # Clean old session files (older than 7 days)
    find "$APP_DIR/sessions" -name "*.json" -mtime +7 -delete 2>/dev/null || true
    
    log_success "Cleanup completed"
}

# Main monitoring function
main() {
    local mode="${1:-check}"
    
    # Create log file if it doesn't exist
    mkdir -p "$(dirname "$LOG_FILE")"
    touch "$LOG_FILE"
    
    case "$mode" in
        check)
            log_info "Starting health check..."
            check_process
            check_resources
            check_logs
            check_data_freshness
            log_success "Health check completed"
            ;;
        restart)
            log_info "Starting restart check..."
            restart_if_needed
            ;;
        cleanup)
            log_info "Starting cleanup..."
            cleanup_old_files
            ;;
        full)
            log_info "Starting full monitoring cycle..."
            check_process
            check_resources
            check_logs
            check_data_freshness
            restart_if_needed
            cleanup_old_files
            log_success "Full monitoring cycle completed"
            ;;
        *)
            echo "Usage: $0 [check|restart|cleanup|full]"
            echo ""
            echo "Modes:"
            echo "  check   - Perform health checks only"
            echo "  restart - Check and restart if needed"
            echo "  cleanup - Clean up old files"
            echo "  full    - Full monitoring cycle"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
