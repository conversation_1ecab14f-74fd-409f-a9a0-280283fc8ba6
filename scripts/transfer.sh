#!/bin/bash

# Chotot House Rent Crawler File Transfer Script
# This script transfers application files to the server efficiently

set -e

# Configuration (modify these variables)
DEFAULT_SERVER="<EMAIL>"
DEFAULT_REMOTE_PATH="/usr/src/app"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Show help
show_help() {
    echo "Chotot House Rent Crawler File Transfer Script"
    echo ""
    echo "Usage: $0 [OPTIONS] [SERVER] [REMOTE_PATH]"
    echo ""
    echo "Arguments:"
    echo "  SERVER      SSH connection string (user@hostname)"
    echo "  REMOTE_PATH Remote directory path (default: /usr/src/app)"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -d, --dry-run  Show what would be transferred without actually doing it"
    echo "  -f, --full     Include all files (don't exclude output, sessions, etc.)"
    echo "  -u, --update   Update mode - delete files on server that don't exist locally"
    echo "  -v, --verbose  Verbose output"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Use default server settings"
    echo "  $0 <EMAIL>                 # Specify server"
    echo "  $0 <EMAIL> /opt/crawler    # Specify server and path"
    echo "  $0 -d <EMAIL>              # Dry run to see what would be transferred"
    echo "  $0 -u <EMAIL>              # Update mode (delete extra files)"
    echo ""
    echo "Configuration:"
    echo "  Edit the DEFAULT_SERVER and DEFAULT_REMOTE_PATH variables in this script"
    echo "  or set them as environment variables:"
    echo "    export CRAWLER_SERVER=<EMAIL>"
    echo "    export CRAWLER_REMOTE_PATH=/usr/src/app"
}

# Parse command line arguments
DRY_RUN=false
FULL_TRANSFER=false
UPDATE_MODE=false
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -f|--full)
            FULL_TRANSFER=true
            shift
            ;;
        -u|--update)
            UPDATE_MODE=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -*)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
        *)
            if [ -z "$SERVER" ]; then
                SERVER="$1"
            elif [ -z "$REMOTE_PATH" ]; then
                REMOTE_PATH="$1"
            else
                log_error "Too many arguments"
                show_help
                exit 1
            fi
            shift
            ;;
    esac
done

# Set defaults
SERVER="${SERVER:-${CRAWLER_SERVER:-$DEFAULT_SERVER}}"
REMOTE_PATH="${REMOTE_PATH:-${CRAWLER_REMOTE_PATH:-$DEFAULT_REMOTE_PATH}}"

# Validate configuration
if [ "$SERVER" = "$DEFAULT_SERVER" ]; then
    log_warning "Using default server configuration. Please update the script or use command line arguments."
    log_warning "Server: $SERVER"
    log_warning "Remote path: $REMOTE_PATH"
    echo ""
    read -p "Continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Transfer cancelled"
        exit 0
    fi
fi

# Test SSH connection
log_info "Testing SSH connection to $SERVER..."
if ! ssh -o ConnectTimeout=10 -o BatchMode=yes "$SERVER" exit 2>/dev/null; then
    log_error "Cannot connect to $SERVER"
    log_error "Please check:"
    log_error "  1. Server hostname/IP is correct"
    log_error "  2. SSH key is configured"
    log_error "  3. User has access to the server"
    exit 1
fi
log_success "SSH connection successful"

# Build rsync command
RSYNC_CMD="rsync"

# Add options
if [ "$VERBOSE" = true ]; then
    RSYNC_CMD="$RSYNC_CMD -avz --progress"
else
    RSYNC_CMD="$RSYNC_CMD -az --progress"
fi

if [ "$DRY_RUN" = true ]; then
    RSYNC_CMD="$RSYNC_CMD --dry-run"
    log_info "DRY RUN MODE - No files will be transferred"
fi

if [ "$UPDATE_MODE" = true ]; then
    RSYNC_CMD="$RSYNC_CMD --delete"
    log_warning "UPDATE MODE - Files on server that don't exist locally will be deleted"
fi

# Add exclusions (unless full transfer is requested)
if [ "$FULL_TRANSFER" = false ]; then
    RSYNC_CMD="$RSYNC_CMD --exclude=node_modules"
    RSYNC_CMD="$RSYNC_CMD --exclude=.git"
    RSYNC_CMD="$RSYNC_CMD --exclude=output"
    RSYNC_CMD="$RSYNC_CMD --exclude=sessions"
    RSYNC_CMD="$RSYNC_CMD --exclude=logs"
    RSYNC_CMD="$RSYNC_CMD --exclude=data"
    RSYNC_CMD="$RSYNC_CMD --exclude=*.log"
    RSYNC_CMD="$RSYNC_CMD --exclude=.DS_Store"
    RSYNC_CMD="$RSYNC_CMD --exclude=Thumbs.db"
    RSYNC_CMD="$RSYNC_CMD --exclude=.env"
    RSYNC_CMD="$RSYNC_CMD --exclude=*.tmp"
    RSYNC_CMD="$RSYNC_CMD --exclude=*.swp"
fi

# Add source and destination
RSYNC_CMD="$RSYNC_CMD . $SERVER:$REMOTE_PATH/"

# Show what will be executed
log_info "Transfer command:"
echo "  $RSYNC_CMD"
echo ""

if [ "$DRY_RUN" = false ]; then
    read -p "Proceed with file transfer? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Transfer cancelled"
        exit 0
    fi
fi

# Create remote directory if it doesn't exist
log_info "Ensuring remote directory exists..."
ssh "$SERVER" "sudo mkdir -p $REMOTE_PATH && sudo chown -R \$(whoami):\$(whoami) $REMOTE_PATH" || {
    log_error "Failed to create remote directory"
    exit 1
}

# Execute transfer
log_info "Starting file transfer..."
echo "Source: $(pwd)"
echo "Destination: $SERVER:$REMOTE_PATH"
echo ""

if eval "$RSYNC_CMD"; then
    if [ "$DRY_RUN" = false ]; then
        log_success "✅ File transfer completed successfully!"
        
        # Set proper permissions on server
        log_info "Setting proper permissions..."
        ssh "$SERVER" "find $REMOTE_PATH -name '*.sh' -exec chmod +x {} \;" || {
            log_warning "Failed to set script permissions"
        }
        
        echo ""
        log_success "🎉 Deployment files are ready on the server!"
        echo ""
        log_info "📝 Next steps:"
        echo "   1. SSH to server: ssh $SERVER"
        echo "   2. Navigate to app: cd $REMOTE_PATH"
        echo "   3. Configure environment: cp .env.example .env && nano .env"
        echo "   4. Run deployment: sudo ./scripts/deploy.sh [setup|docker|native]"
        echo ""
        log_info "💡 Quick deployment commands:"
        echo "   ssh $SERVER 'cd $REMOTE_PATH && sudo ./scripts/deploy.sh setup'"
        echo "   ssh $SERVER 'cd $REMOTE_PATH && sudo ./scripts/deploy.sh docker'"
    else
        log_info "Dry run completed - no files were transferred"
    fi
else
    log_error "❌ File transfer failed"
    exit 1
fi
