/**
 * CAPTCHA solving configuration example
 * Copy this file to captcha.config.js and update with your API keys
 */

module.exports = {
  // 2captcha.com configuration
  twoCaptcha: {
    apiKey: 'your-2captcha-api-key',
    timeout: 120000, // 2 minutes
    pollingInterval: 5000 // 5 seconds
  },

  // anti-captcha.com configuration
  antiCaptcha: {
    apiKey: 'your-anticaptcha-api-key',
    timeout: 120000,
    pollingInterval: 5000
  },

  // Manual solving settings
  manual: {
    enabled: true,
    timeout: 300000, // 5 minutes for manual solving
    showBrowser: true // Show browser window for manual solving
  },

  // CAPTCHA detection settings
  detection: {
    checkInterval: 1000, // Check for CAPTCHAs every second
    maxRetries: 3,
    autoSolve: false // Set to true to automatically solve CAPTCHAs
  }
};
