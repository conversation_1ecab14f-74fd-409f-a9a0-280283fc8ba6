/**
 * Configuration loader for the Rental Property Crawler
 * Loads configuration based on NODE_ENV and merges with defaults
 */

const fs = require('fs');
const path = require('path');
const defaultConfig = require('./default');

class ConfigLoader {
  constructor() {
    this.config = null;
    this.loadConfig();
  }

  loadConfig() {
    // Start with default configuration
    this.config = { ...defaultConfig };

    // Load environment-specific configuration
    const env = process.env.NODE_ENV || 'development';
    const envConfigPath = path.join(__dirname, `${env}.js`);
    
    if (fs.existsSync(envConfigPath)) {
      const envConfig = require(envConfigPath);
      this.config = this.mergeDeep(this.config, envConfig);
      console.log(`📝 Loaded ${env} configuration`);
    }

    // Load external configurations (proxy, captcha)
    this.loadExternalConfigs();

    // Override with environment variables
    this.loadEnvironmentVariables();
  }

  loadExternalConfigs() {
    // Load proxy configuration
    const proxyConfigPath = path.join(process.cwd(), 'proxy.config.js');
    if (fs.existsSync(proxyConfigPath)) {
      try {
        const proxyConfig = require(proxyConfigPath);
        this.config.proxy = { ...this.config.proxy, ...proxyConfig };
        console.log('🔗 Loaded proxy configuration');
      } catch (error) {
        console.warn('⚠️ Failed to load proxy configuration:', error.message);
      }
    }

    // Load CAPTCHA configuration
    const captchaConfigPath = path.join(process.cwd(), 'captcha.config.js');
    if (fs.existsSync(captchaConfigPath)) {
      try {
        const captchaConfig = require(captchaConfigPath);
        this.config.captcha = { ...this.config.captcha, ...captchaConfig };
        console.log('🤖 Loaded CAPTCHA configuration');
      } catch (error) {
        console.warn('⚠️ Failed to load CAPTCHA configuration:', error.message);
      }
    }
  }

  loadEnvironmentVariables() {
    // Override configuration with environment variables
    if (process.env.BROWSER_HEADLESS !== undefined) {
      this.config.browser.headless = process.env.BROWSER_HEADLESS === 'true';
    }

    if (process.env.CRAWLING_MAX_PAGES) {
      this.config.crawling.maxPages = parseInt(process.env.CRAWLING_MAX_PAGES);
    }

    if (process.env.PROXY_ENABLED !== undefined) {
      this.config.proxy.enabled = process.env.PROXY_ENABLED === 'true';
    }

    if (process.env.PROXY_HOST) {
      this.config.proxy.host = process.env.PROXY_HOST;
    }

    if (process.env.PROXY_PORT) {
      this.config.proxy.port = parseInt(process.env.PROXY_PORT);
    }

    if (process.env.SLACK_WEBHOOK_URL) {
      this.config.slack = this.config.slack || {};
      this.config.slack.webhookUrl = process.env.SLACK_WEBHOOK_URL;
    }
  }

  mergeDeep(target, source) {
    const output = Object.assign({}, target);
    if (this.isObject(target) && this.isObject(source)) {
      Object.keys(source).forEach(key => {
        if (this.isObject(source[key])) {
          if (!(key in target))
            Object.assign(output, { [key]: source[key] });
          else
            output[key] = this.mergeDeep(target[key], source[key]);
        } else {
          Object.assign(output, { [key]: source[key] });
        }
      });
    }
    return output;
  }

  isObject(item) {
    return item && typeof item === 'object' && !Array.isArray(item);
  }

  get() {
    return this.config;
  }

  reload() {
    this.loadConfig();
    return this.config;
  }
}

// Export singleton instance
const configLoader = new ConfigLoader();
module.exports = configLoader.get();
