/**
 * Production configuration overrides
 */

module.exports = {
  // Browser settings for production
  browser: {
    headless: true, // Always headless in production
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--disable-gpu',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding',
      '--disable-features=TranslateUI',
      '--disable-ipc-flooding-protection',
      '--memory-pressure-off'
    ]
  },

  // More conservative crawling settings for production
  crawling: {
    delay: {
      min: 3000, // Longer delays in production
      max: 8000
    },
    retries: 5, // More retries in production
    timeout: 60000, // Longer timeout for production
    maxPages: 20 // More pages in production
  },

  // Output settings for production
  output: {
    directory: './output',
    format: ['json'], // Only J<PERSON><PERSON> in production to save space
    filename: 'rental_properties_{timestamp}',
    saveImages: true,
    maxFileSize: 50 * 1024 * 1024 // 50MB max file size
  },

  // Slack notifications enabled in production
  slack: {
    enabled: true,
    maxPropertiesPerMessage: 10,
    sendErrorNotifications: true,
    sendSuccessNotifications: true
  },

  // Logging configuration for production
  logging: {
    level: 'info',
    file: './logs/crawler.log',
    maxFiles: 10,
    maxSize: '10m'
  }
};
