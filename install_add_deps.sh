#!/bin/bash

# Cập nhật danh sách gói
echo "Cập nhật danh sách gói..."
sudo apt update -y

# Cài đặt các package được liệt kê
echo "Cài đặt các package hệ thống..."
sudo apt install -y \
    ca-certificates \
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libc6 \
    libcairo2 \
    libcups2 \
    libdbus-1-3 \
    libexpat1 \
    libfontconfig1 \
    libgbm1 \
    libgcc1 \
    libglib2.0-0 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libstdc++6 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrandr2 \
    libxrender1 \
    libxss1 \
    libxtst6 \
    lsb-release \
    wget \
    xdg-utils

# Kiểm tra xem việc cài đặt có thành công không
if [ $? -eq 0 ]; then
    echo "Tất cả các package đã được cài đặt thành công."
else
    echo "Có lỗi xảy ra trong quá trình cài đặt các package."
fi